<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">项目管理功能测试</h1>
        
        <div class="mb-4">
            <button onclick="testLoadProjects()" class="bg-blue-600 text-white px-4 py-2 rounded mr-2">
                测试加载项目
            </button>
            <button onclick="testAPI()" class="bg-green-600 text-white px-4 py-2 rounded mr-2">
                测试API
            </button>
            <button onclick="loadProjectsPage()" class="bg-purple-600 text-white px-4 py-2 rounded">
                加载项目页面
            </button>
        </div>

        <div id="test-results" class="bg-white p-4 rounded shadow">
            <p>点击按钮测试功能...</p>
        </div>

        <div id="projects-page" class="mt-6">
            <!-- 项目管理内容将在这里显示 -->
        </div>
    </div>

    <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

    <script>
        // API 基础URL
        const API_BASE = '/api';

        // 简化的通知功能
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            notification.className = `mb-2 p-4 rounded-lg shadow-lg ${getNotificationClass(type)}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 hover:opacity-75">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        function getNotificationClass(type) {
            switch(type) {
                case 'success': return 'bg-green-500 text-white';
                case 'error': return 'bg-red-500 text-white';
                case 'warning': return 'bg-yellow-500 text-white';
                default: return 'bg-blue-500 text-white';
            }
        }

        // 工具函数
        function getStatusClass(status) {
            switch(status) {
                case 'passed': return 'bg-green-100 text-green-800';
                case 'failed': return 'bg-red-100 text-red-800';
                case 'pending': return 'bg-gray-100 text-gray-800';
                case 'running': return 'bg-blue-100 text-blue-800';
                case 'skipped': return 'bg-yellow-100 text-yellow-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'passed': return '已通过';
                case 'failed': return '失败';
                case 'pending': return '待执行';
                case 'running': return '执行中';
                case 'skipped': return '跳过';
                case 'draft': return '草稿';
                default: return '未知';
            }
        }

        function getPriorityClass(priority) {
            switch(priority) {
                case 'high': return 'bg-red-100 text-red-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                case 'low': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getPriorityText(priority) {
            switch(priority) {
                case 'high': return '高';
                case 'medium': return '中';
                case 'low': return '低';
                default: return '未知';
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 测试函数
        async function testAPI() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>测试API连接...</p>';

            try {
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                results.innerHTML = `
                    <h3 class="font-bold mb-2">API测试结果:</h3>
                    <p>状态: ${response.ok ? '成功' : '失败'}</p>
                    <p>项目数量: ${data.length}</p>
                    <pre class="bg-gray-100 p-2 mt-2 text-sm overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
                
                showNotification('API测试成功', 'success');
            } catch (error) {
                results.innerHTML = `<p class="text-red-600">API测试失败: ${error.message}</p>`;
                showNotification('API测试失败', 'error');
            }
        }

        async function testLoadProjects() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p>测试加载项目函数...</p>';

            try {
                if (typeof loadProjects === 'function') {
                    await loadProjects();
                    results.innerHTML = '<p class="text-green-600">loadProjects函数执行成功</p>';
                    showNotification('loadProjects函数测试成功', 'success');
                } else {
                    results.innerHTML = '<p class="text-red-600">loadProjects函数未定义</p>';
                    showNotification('loadProjects函数未定义', 'error');
                }
            } catch (error) {
                results.innerHTML = `<p class="text-red-600">loadProjects函数执行失败: ${error.message}</p>`;
                showNotification('loadProjects函数执行失败', 'error');
            }
        }

        // 暴露全局函数
        window.showNotification = showNotification;
        window.getStatusClass = getStatusClass;
        window.getStatusText = getStatusText;
        window.getPriorityClass = getPriorityClass;
        window.getPriorityText = getPriorityText;
        window.formatDate = formatDate;
        window.API_BASE = API_BASE;
    </script>

    <!-- 引入项目管理脚本 -->
    <script src="js/projects.js"></script>
</body>
</html>
