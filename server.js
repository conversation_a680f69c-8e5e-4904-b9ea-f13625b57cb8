const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// 数据库连接
const db = new sqlite3.Database('./database.db', (err) => {
    if (err) {
        console.error('Error opening database:', err);
    } else {
        console.log('Connected to SQLite database');
        initDatabase();
    }
});

// 初始化数据库
function initDatabase() {
    // 创建项目表
    db.run(`CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'active',
        owner TEXT,
        start_date DATE,
        end_date DATE,
        priority TEXT DEFAULT 'medium',
        tags TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // 检查并添加缺失的列（用于升级现有数据库）
    const addColumnIfNotExists = (tableName, columnName, columnDef) => {
        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                console.error('Error checking table info:', err);
                return;
            }

            const columnExists = columns.some(col => col.name === columnName);
            if (!columnExists) {
                db.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDef}`, (err) => {
                    if (err) {
                        console.error(`Error adding column ${columnName}:`, err);
                    } else {
                        console.log(`Added column ${columnName} to ${tableName}`);
                    }
                });
            }
        });
    };

    // 添加可能缺失的列
    addColumnIfNotExists('projects', 'owner', 'TEXT');
    addColumnIfNotExists('projects', 'start_date', 'TEXT');
    addColumnIfNotExists('projects', 'end_date', 'TEXT');
    addColumnIfNotExists('projects', 'priority', 'TEXT DEFAULT "medium"');
    addColumnIfNotExists('projects', 'status', 'TEXT DEFAULT "active"');
    addColumnIfNotExists('projects', 'tags', 'TEXT');
    addColumnIfNotExists('projects', 'updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP');

    // 创建项目成员表
    db.run(`CREATE TABLE IF NOT EXISTS project_members (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        user_name TEXT,
        role TEXT DEFAULT 'member',
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`);

    // 创建测试用例表
    db.run(`CREATE TABLE IF NOT EXISTS testcases (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        project_id TEXT,
        priority TEXT DEFAULT 'medium',
        type TEXT DEFAULT 'functional',
        execution_type TEXT DEFAULT 'manual',
        status TEXT DEFAULT 'draft',
        precondition TEXT,
        steps TEXT,
        expected_result TEXT,
        creator TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_execution DATETIME,
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`);

    // 创建测试执行记录表
    db.run(`CREATE TABLE IF NOT EXISTS test_executions (
        id TEXT PRIMARY KEY,
        testcase_id TEXT,
        environment TEXT,
        status TEXT,
        result TEXT,
        notes TEXT,
        executor TEXT,
        execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (testcase_id) REFERENCES testcases (id)
    )`);

    // 插入示例数据
    insertSampleData();
}

function insertSampleData() {
    // 检查是否已有数据
    db.get("SELECT COUNT(*) as count FROM projects", (err, row) => {
        if (err) {
            console.error(err);
            return;
        }
        
        if (row.count === 0) {
            // 插入示例项目
            const projects = [
                {
                    id: uuidv4(),
                    name: '电商平台测试',
                    description: '电商平台核心功能测试项目，包含用户管理、商品管理、订单处理等模块的全面测试',
                    owner: '等等何在',
                    start_date: '2023-01-15',
                    end_date: '2023-12-31',
                    priority: 'high',
                    tags: JSON.stringify(['电商', '核心业务', 'Web'])
                },
                {
                    id: uuidv4(),
                    name: '移动端APP测试',
                    description: 'iOS和Android移动应用的功能测试、兼容性测试和性能测试',
                    owner: '张测试',
                    start_date: '2023-03-01',
                    end_date: '2023-11-30',
                    priority: 'medium',
                    tags: JSON.stringify(['移动端', 'iOS', 'Android'])
                },
                {
                    id: uuidv4(),
                    name: '后台管理系统',
                    description: '管理后台的权限管理、数据统计、系统配置等功能测试',
                    owner: '李开发',
                    start_date: '2023-02-01',
                    end_date: '2023-10-31',
                    priority: 'medium',
                    tags: JSON.stringify(['后台', '管理系统', 'Web'])
                }
            ];

            projects.forEach(project => {
                db.run(`INSERT INTO projects (
                    id, name, description, owner, start_date, end_date, priority, tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    project.id, project.name, project.description, project.owner,
                    project.start_date, project.end_date, project.priority, project.tags
                ]);
            });

            // 插入项目成员数据
            const members = [
                { id: uuidv4(), project_id: projects[0].id, user_name: '等等何在', role: 'owner' },
                { id: uuidv4(), project_id: projects[0].id, user_name: '张测试', role: 'tester' },
                { id: uuidv4(), project_id: projects[0].id, user_name: '李开发', role: 'developer' },
                { id: uuidv4(), project_id: projects[1].id, user_name: '张测试', role: 'owner' },
                { id: uuidv4(), project_id: projects[1].id, user_name: '王产品', role: 'member' },
                { id: uuidv4(), project_id: projects[2].id, user_name: '李开发', role: 'owner' },
                { id: uuidv4(), project_id: projects[2].id, user_name: '等等何在', role: 'tester' }
            ];

            members.forEach(member => {
                db.run(`INSERT INTO project_members (id, project_id, user_name, role) VALUES (?, ?, ?, ?)`,
                    [member.id, member.project_id, member.user_name, member.role]);
            });

            // 插入示例测试用例
            const testcases = [
                {
                    id: uuidv4(),
                    name: '用户登录功能测试',
                    description: '验证用户名密码登录流程',
                    project_id: projects[0].id,
                    priority: 'high',
                    type: 'functional',
                    execution_type: 'automated',
                    status: 'passed',
                    precondition: '用户已注册且账号状态正常',
                    steps: JSON.stringify(['打开登录页面', '输入用户名和密码', '点击登录按钮']),
                    expected_result: '用户成功登录，跳转到首页',
                    creator: '张测试'
                },
                {
                    id: uuidv4(),
                    name: '购物车添加商品测试',
                    description: '验证商品添加到购物车功能',
                    project_id: projects[0].id,
                    priority: 'medium',
                    type: 'functional',
                    execution_type: 'automated',
                    status: 'failed',
                    precondition: '用户已登录，商品库存充足',
                    steps: JSON.stringify(['浏览商品页面', '选择商品规格', '点击加入购物车']),
                    expected_result: '商品成功添加到购物车，购物车数量更新',
                    creator: '李开发'
                },
                {
                    id: uuidv4(),
                    name: '订单支付流程测试',
                    description: '验证订单支付完整流程',
                    project_id: projects[0].id,
                    priority: 'high',
                    type: 'functional',
                    execution_type: 'manual',
                    status: 'pending',
                    precondition: '用户已登录，购物车有商品',
                    steps: JSON.stringify(['进入购物车', '点击结算', '填写收货信息', '选择支付方式', '完成支付']),
                    expected_result: '订单支付成功，生成订单号',
                    creator: '王产品'
                }
            ];

            testcases.forEach(testcase => {
                db.run(`INSERT INTO testcases (
                    id, name, description, project_id, priority, type, execution_type, 
                    status, precondition, steps, expected_result, creator
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, 
                [
                    testcase.id, testcase.name, testcase.description, testcase.project_id,
                    testcase.priority, testcase.type, testcase.execution_type, testcase.status,
                    testcase.precondition, testcase.steps, testcase.expected_result, testcase.creator
                ]);
            });

            console.log('Sample data inserted successfully');
        }
    });
}

// API 路由

// 获取所有项目（支持筛选和搜索）
app.get('/api/projects', (req, res) => {
    const { status, owner, priority, search } = req.query;

    let sql = `
        SELECT p.*,
               COUNT(DISTINCT pm.id) as member_count,
               COUNT(DISTINCT t.id) as testcase_count
        FROM projects p
        LEFT JOIN project_members pm ON p.id = pm.project_id
        LEFT JOIN testcases t ON p.id = t.project_id
        WHERE 1=1
    `;
    const params = [];

    if (status) {
        sql += ' AND p.status = ?';
        params.push(status);
    }
    if (owner) {
        sql += ' AND p.owner = ?';
        params.push(owner);
    }
    if (priority) {
        sql += ' AND p.priority = ?';
        params.push(priority);
    }
    if (search) {
        sql += ' AND (p.name LIKE ? OR p.description LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
    }

    sql += ' GROUP BY p.id ORDER BY p.created_at DESC';

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 解析tags JSON
        const projects = rows.map(row => ({
            ...row,
            tags: row.tags ? JSON.parse(row.tags) : []
        }));

        res.json(projects);
    });
});

// 获取单个项目详情
app.get('/api/projects/:id', (req, res) => {
    const { id } = req.params;

    // 获取项目基本信息
    db.get(`
        SELECT p.*,
               COUNT(DISTINCT pm.id) as member_count,
               COUNT(DISTINCT t.id) as testcase_count
        FROM projects p
        LEFT JOIN project_members pm ON p.id = pm.project_id
        LEFT JOIN testcases t ON p.id = t.project_id
        WHERE p.id = ?
        GROUP BY p.id
    `, [id], (err, project) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!project) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }

        // 获取项目成员
        db.all(`
            SELECT * FROM project_members WHERE project_id = ? ORDER BY joined_at
        `, [id], (err, members) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取项目测试用例统计
            db.all(`
                SELECT status, COUNT(*) as count
                FROM testcases
                WHERE project_id = ?
                GROUP BY status
            `, [id], (err, testcaseStats) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }

                const result = {
                    ...project,
                    tags: project.tags ? JSON.parse(project.tags) : [],
                    members,
                    testcase_stats: testcaseStats
                };

                res.json(result);
            });
        });
    });
});

// 创建项目
app.post('/api/projects', (req, res) => {
    const {
        name, description, owner, start_date, end_date, priority, tags
    } = req.body;

    if (!name) {
        res.status(400).json({ error: 'Project name is required' });
        return;
    }

    const id = uuidv4();
    const tagsJson = JSON.stringify(tags || []);

    db.run(`
        INSERT INTO projects (
            id, name, description, owner, start_date, end_date, priority, tags
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
        id, name, description, owner, start_date, end_date,
        priority || 'medium', tagsJson
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 如果指定了owner，自动添加为项目成员
        if (owner) {
            const memberId = uuidv4();
            db.run(`
                INSERT INTO project_members (id, project_id, user_name, role)
                VALUES (?, ?, ?, ?)
            `, [memberId, id, owner, 'owner']);
        }

        res.json({ id, message: 'Project created successfully' });
    });
});

// 更新项目
app.put('/api/projects/:id', (req, res) => {
    const { id } = req.params;
    const {
        name, description, owner, start_date, end_date, priority, tags, status
    } = req.body;

    const tagsJson = JSON.stringify(tags || []);

    db.run(`
        UPDATE projects SET
            name = ?, description = ?, owner = ?, start_date = ?,
            end_date = ?, priority = ?, tags = ?, status = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `, [
        name, description, owner, start_date, end_date,
        priority, tagsJson, status, id
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }
        res.json({ message: 'Project updated successfully' });
    });
});

// 删除项目
app.delete('/api/projects/:id', (req, res) => {
    const { id } = req.params;

    // 检查项目是否有测试用例
    db.get('SELECT COUNT(*) as count FROM testcases WHERE project_id = ?', [id], (err, result) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (result.count > 0) {
            res.status(400).json({
                error: 'Cannot delete project with existing test cases',
                testcase_count: result.count
            });
            return;
        }

        // 删除项目成员
        db.run('DELETE FROM project_members WHERE project_id = ?', [id], (err) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 删除项目
            db.run('DELETE FROM projects WHERE id = ?', [id], function(err) {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }
                if (this.changes === 0) {
                    res.status(404).json({ error: 'Project not found' });
                    return;
                }
                res.json({ message: 'Project deleted successfully' });
            });
        });
    });
});

// 项目成员管理API

// 添加项目成员
app.post('/api/projects/:id/members', (req, res) => {
    const { id } = req.params;
    const { user_name, role } = req.body;

    if (!user_name) {
        res.status(400).json({ error: 'User name is required' });
        return;
    }

    // 检查成员是否已存在
    db.get(`
        SELECT * FROM project_members
        WHERE project_id = ? AND user_name = ?
    `, [id, user_name], (err, existing) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (existing) {
            res.status(400).json({ error: 'User is already a member of this project' });
            return;
        }

        const memberId = uuidv4();
        db.run(`
            INSERT INTO project_members (id, project_id, user_name, role)
            VALUES (?, ?, ?, ?)
        `, [memberId, id, user_name, role || 'member'], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: memberId, message: 'Member added successfully' });
        });
    });
});

// 更新项目成员角色
app.put('/api/projects/:id/members/:memberId', (req, res) => {
    const { memberId } = req.params;
    const { role } = req.body;

    db.run(`
        UPDATE project_members SET role = ? WHERE id = ?
    `, [role, memberId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Member not found' });
            return;
        }
        res.json({ message: 'Member role updated successfully' });
    });
});

// 移除项目成员
app.delete('/api/projects/:id/members/:memberId', (req, res) => {
    const { memberId } = req.params;

    db.run('DELETE FROM project_members WHERE id = ?', [memberId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Member not found' });
            return;
        }
        res.json({ message: 'Member removed successfully' });
    });
});

// 获取所有测试用例
app.get('/api/testcases', (req, res) => {
    const { project_id, status, priority, creator, search } = req.query;
    
    let sql = `
        SELECT t.*, p.name as project_name 
        FROM testcases t 
        LEFT JOIN projects p ON t.project_id = p.id 
        WHERE 1=1
    `;
    const params = [];

    if (project_id) {
        sql += ' AND t.project_id = ?';
        params.push(project_id);
    }
    if (status) {
        sql += ' AND t.status = ?';
        params.push(status);
    }
    if (priority) {
        sql += ' AND t.priority = ?';
        params.push(priority);
    }
    if (creator) {
        sql += ' AND t.creator = ?';
        params.push(creator);
    }
    if (search) {
        sql += ' AND (t.name LIKE ? OR t.description LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
    }

    sql += ' ORDER BY t.created_at DESC';

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        
        // 解析步骤JSON
        const testcases = rows.map(row => ({
            ...row,
            steps: row.steps ? JSON.parse(row.steps) : []
        }));
        
        res.json(testcases);
    });
});

// 获取单个测试用例
app.get('/api/testcases/:id', (req, res) => {
    const { id } = req.params;
    
    db.get(`
        SELECT t.*, p.name as project_name 
        FROM testcases t 
        LEFT JOIN projects p ON t.project_id = p.id 
        WHERE t.id = ?
    `, [id], (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!row) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        
        // 解析步骤JSON
        const testcase = {
            ...row,
            steps: row.steps ? JSON.parse(row.steps) : []
        };
        
        res.json(testcase);
    });
});

// 创建测试用例
app.post('/api/testcases', (req, res) => {
    const {
        name, description, project_id, priority, type, execution_type,
        precondition, steps, expected_result, creator
    } = req.body;

    if (!name || !project_id) {
        res.status(400).json({ error: 'Name and project_id are required' });
        return;
    }

    const id = uuidv4();
    const stepsJson = JSON.stringify(steps || []);

    db.run(`
        INSERT INTO testcases (
            id, name, description, project_id, priority, type, execution_type,
            precondition, steps, expected_result, creator
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
        id, name, description, project_id, priority || 'medium', 
        type || 'functional', execution_type || 'manual',
        precondition, stepsJson, expected_result, creator
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id, message: 'Test case created successfully' });
    });
});

// 更新测试用例
app.put('/api/testcases/:id', (req, res) => {
    const { id } = req.params;
    const {
        name, description, project_id, priority, type, execution_type,
        precondition, steps, expected_result, status
    } = req.body;

    const stepsJson = JSON.stringify(steps || []);

    db.run(`
        UPDATE testcases SET 
            name = ?, description = ?, project_id = ?, priority = ?, 
            type = ?, execution_type = ?, precondition = ?, steps = ?, 
            expected_result = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `, [
        name, description, project_id, priority, type, execution_type,
        precondition, stepsJson, expected_result, status, id
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        res.json({ message: 'Test case updated successfully' });
    });
});

// 删除测试用例
app.delete('/api/testcases/:id', (req, res) => {
    const { id } = req.params;

    db.run('DELETE FROM testcases WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        res.json({ message: 'Test case deleted successfully' });
    });
});

// 批量删除测试用例
app.delete('/api/testcases', (req, res) => {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
        res.status(400).json({ error: 'IDs array is required' });
        return;
    }

    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM testcases WHERE id IN (${placeholders})`;

    db.run(sql, ids, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ 
            message: `${this.changes} test case(s) deleted successfully`,
            deletedCount: this.changes 
        });
    });
});

// 执行测试用例
app.post('/api/testcases/:id/execute', (req, res) => {
    const { id } = req.params;
    const { environment, notes, executor } = req.body;

    const executionId = uuidv4();
    const status = 'running';

    // 创建执行记录
    db.run(`
        INSERT INTO test_executions (id, testcase_id, environment, status, notes, executor)
        VALUES (?, ?, ?, ?, ?, ?)
    `, [executionId, id, environment, status, notes, executor], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 更新测试用例的最后执行时间
        db.run(`
            UPDATE testcases SET last_execution = CURRENT_TIMESTAMP WHERE id = ?
        `, [id]);

        res.json({ 
            executionId,
            message: 'Test case execution started',
            status: 'running'
        });
    });
});

// 静态文件服务
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    db.close((err) => {
        if (err) {
            console.error(err.message);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});
