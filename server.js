const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const http = require('http');
const WebSocket = require('ws');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// 数据库连接
const db = new sqlite3.Database('./database.db', (err) => {
    if (err) {
        console.error('Error opening database:', err);
    } else {
        console.log('Connected to SQLite database');
        initDatabase();
    }
});

// 初始化数据库
function initDatabase() {
    // 创建项目表
    db.run(`CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'active',
        owner TEXT,
        start_date DATE,
        end_date DATE,
        priority TEXT DEFAULT 'medium',
        tags TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // 检查并添加缺失的列（用于升级现有数据库）
    const addColumnIfNotExists = (tableName, columnName, columnDef) => {
        db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
                console.error('Error checking table info:', err);
                return;
            }

            const columnExists = columns.some(col => col.name === columnName);
            if (!columnExists) {
                db.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDef}`, (err) => {
                    if (err) {
                        console.error(`Error adding column ${columnName}:`, err);
                    } else {
                        console.log(`Added column ${columnName} to ${tableName}`);
                    }
                });
            }
        });
    };

    // 添加可能缺失的列
    addColumnIfNotExists('projects', 'owner', 'TEXT');
    addColumnIfNotExists('projects', 'start_date', 'TEXT');
    addColumnIfNotExists('projects', 'end_date', 'TEXT');
    addColumnIfNotExists('projects', 'priority', 'TEXT DEFAULT "medium"');
    addColumnIfNotExists('projects', 'status', 'TEXT DEFAULT "active"');
    addColumnIfNotExists('projects', 'tags', 'TEXT');
    addColumnIfNotExists('projects', 'updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP');

    // 创建测试套件表
    db.run(`CREATE TABLE IF NOT EXISTS test_suites (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        project_id TEXT NOT NULL,
        testcase_ids TEXT, -- JSON数组存储测试用例ID
        created_by TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`);

    // 创建执行环境配置表
    db.run(`CREATE TABLE IF NOT EXISTS execution_environments (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL, -- 'local', 'jenkins', 'remote'
        config TEXT, -- JSON配置信息
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // 创建测试执行记录表
    db.run(`CREATE TABLE IF NOT EXISTS test_executions (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        suite_id TEXT, -- 可选，如果是套件执行
        testcase_ids TEXT NOT NULL, -- JSON数组存储执行的测试用例ID
        environment_id TEXT NOT NULL,
        status TEXT DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed', 'cancelled'
        progress INTEGER DEFAULT 0, -- 执行进度百分比
        current_testcase_id TEXT, -- 当前执行的测试用例ID
        total_count INTEGER DEFAULT 0,
        passed_count INTEGER DEFAULT 0,
        failed_count INTEGER DEFAULT 0,
        skipped_count INTEGER DEFAULT 0,
        start_time DATETIME,
        end_time DATETIME,
        duration INTEGER, -- 执行时长（秒）
        executor TEXT, -- 执行者
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects (id),
        FOREIGN KEY (suite_id) REFERENCES test_suites (id),
        FOREIGN KEY (environment_id) REFERENCES execution_environments (id)
    )`);

    // 创建测试执行日志表
    db.run(`CREATE TABLE IF NOT EXISTS test_execution_logs (
        id TEXT PRIMARY KEY,
        execution_id TEXT NOT NULL,
        testcase_id TEXT, -- 可选，特定测试用例的日志
        log_level TEXT DEFAULT 'info', -- 'debug', 'info', 'warn', 'error'
        message TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (execution_id) REFERENCES test_executions (id)
    )`);

    // 创建测试用例执行结果表
    db.run(`CREATE TABLE IF NOT EXISTS testcase_execution_results (
        id TEXT PRIMARY KEY,
        execution_id TEXT NOT NULL,
        testcase_id TEXT NOT NULL,
        status TEXT NOT NULL, -- 'passed', 'failed', 'skipped', 'error'
        start_time DATETIME,
        end_time DATETIME,
        duration INTEGER, -- 执行时长（毫秒）
        error_message TEXT,
        stack_trace TEXT,
        screenshots TEXT, -- JSON数组存储截图路径
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (execution_id) REFERENCES test_executions (id),
        FOREIGN KEY (testcase_id) REFERENCES testcases (id)
    )`);

    // 创建项目成员表
    db.run(`CREATE TABLE IF NOT EXISTS project_members (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        user_name TEXT,
        role TEXT DEFAULT 'member',
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`);

    // 创建测试用例表
    db.run(`CREATE TABLE IF NOT EXISTS testcases (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        project_id TEXT,
        priority TEXT DEFAULT 'medium',
        type TEXT DEFAULT 'functional',
        execution_type TEXT DEFAULT 'manual',
        status TEXT DEFAULT 'draft',
        precondition TEXT,
        steps TEXT,
        expected_result TEXT,
        creator TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_execution DATETIME,
        FOREIGN KEY (project_id) REFERENCES projects (id)
    )`);

    // 创建测试执行记录表
    db.run(`CREATE TABLE IF NOT EXISTS test_executions (
        id TEXT PRIMARY KEY,
        testcase_id TEXT,
        environment TEXT,
        status TEXT,
        result TEXT,
        notes TEXT,
        executor TEXT,
        execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (testcase_id) REFERENCES testcases (id)
    )`);

    // 插入示例数据
    insertSampleData();
}

function insertSampleData() {
    // 检查是否已有数据
    db.get("SELECT COUNT(*) as count FROM projects", (err, row) => {
        if (err) {
            console.error(err);
            return;
        }
        
        if (row.count === 0) {
            // 插入示例项目
            const projects = [
                {
                    id: uuidv4(),
                    name: '电商平台测试',
                    description: '电商平台核心功能测试项目，包含用户管理、商品管理、订单处理等模块的全面测试',
                    owner: '等等何在',
                    start_date: '2023-01-15',
                    end_date: '2023-12-31',
                    priority: 'high',
                    tags: JSON.stringify(['电商', '核心业务', 'Web'])
                },
                {
                    id: uuidv4(),
                    name: '移动端APP测试',
                    description: 'iOS和Android移动应用的功能测试、兼容性测试和性能测试',
                    owner: '张测试',
                    start_date: '2023-03-01',
                    end_date: '2023-11-30',
                    priority: 'medium',
                    tags: JSON.stringify(['移动端', 'iOS', 'Android'])
                },
                {
                    id: uuidv4(),
                    name: '后台管理系统',
                    description: '管理后台的权限管理、数据统计、系统配置等功能测试',
                    owner: '李开发',
                    start_date: '2023-02-01',
                    end_date: '2023-10-31',
                    priority: 'medium',
                    tags: JSON.stringify(['后台', '管理系统', 'Web'])
                }
            ];

            projects.forEach(project => {
                db.run(`INSERT INTO projects (
                    id, name, description, owner, start_date, end_date, priority, tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    project.id, project.name, project.description, project.owner,
                    project.start_date, project.end_date, project.priority, project.tags
                ]);
            });

            // 插入项目成员数据
            const members = [
                { id: uuidv4(), project_id: projects[0].id, user_name: '等等何在', role: 'owner' },
                { id: uuidv4(), project_id: projects[0].id, user_name: '张测试', role: 'tester' },
                { id: uuidv4(), project_id: projects[0].id, user_name: '李开发', role: 'developer' },
                { id: uuidv4(), project_id: projects[1].id, user_name: '张测试', role: 'owner' },
                { id: uuidv4(), project_id: projects[1].id, user_name: '王产品', role: 'member' },
                { id: uuidv4(), project_id: projects[2].id, user_name: '李开发', role: 'owner' },
                { id: uuidv4(), project_id: projects[2].id, user_name: '等等何在', role: 'tester' }
            ];

            members.forEach(member => {
                db.run(`INSERT INTO project_members (id, project_id, user_name, role) VALUES (?, ?, ?, ?)`,
                    [member.id, member.project_id, member.user_name, member.role]);
            });

            // 插入示例测试用例
            const testcases = [
                {
                    id: uuidv4(),
                    name: '用户登录功能测试',
                    description: '验证用户名密码登录流程',
                    project_id: projects[0].id,
                    priority: 'high',
                    type: 'functional',
                    execution_type: 'automated',
                    status: 'passed',
                    precondition: '用户已注册且账号状态正常',
                    steps: JSON.stringify(['打开登录页面', '输入用户名和密码', '点击登录按钮']),
                    expected_result: '用户成功登录，跳转到首页',
                    creator: '张测试'
                },
                {
                    id: uuidv4(),
                    name: '购物车添加商品测试',
                    description: '验证商品添加到购物车功能',
                    project_id: projects[0].id,
                    priority: 'medium',
                    type: 'functional',
                    execution_type: 'automated',
                    status: 'failed',
                    precondition: '用户已登录，商品库存充足',
                    steps: JSON.stringify(['浏览商品页面', '选择商品规格', '点击加入购物车']),
                    expected_result: '商品成功添加到购物车，购物车数量更新',
                    creator: '李开发'
                },
                {
                    id: uuidv4(),
                    name: '订单支付流程测试',
                    description: '验证订单支付完整流程',
                    project_id: projects[0].id,
                    priority: 'high',
                    type: 'functional',
                    execution_type: 'manual',
                    status: 'pending',
                    precondition: '用户已登录，购物车有商品',
                    steps: JSON.stringify(['进入购物车', '点击结算', '填写收货信息', '选择支付方式', '完成支付']),
                    expected_result: '订单支付成功，生成订单号',
                    creator: '王产品'
                }
            ];

            testcases.forEach(testcase => {
                db.run(`INSERT INTO testcases (
                    id, name, description, project_id, priority, type, execution_type, 
                    status, precondition, steps, expected_result, creator
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, 
                [
                    testcase.id, testcase.name, testcase.description, testcase.project_id,
                    testcase.priority, testcase.type, testcase.execution_type, testcase.status,
                    testcase.precondition, testcase.steps, testcase.expected_result, testcase.creator
                ]);
            });

            console.log('Sample data inserted successfully');
        }
    });

    // 初始化默认执行环境
    db.get("SELECT COUNT(*) as count FROM execution_environments", (err, row) => {
        if (err) {
            console.error('Error checking execution environments:', err);
            return;
        }

        if (row.count === 0) {
            const environments = [
                {
                    id: uuidv4(),
                    name: '本地环境',
                    type: 'local',
                    config: JSON.stringify({
                        timeout: 300000, // 5分钟超时
                        maxConcurrency: 3,
                        retryCount: 1
                    })
                },
                {
                    id: uuidv4(),
                    name: '开发环境',
                    type: 'remote',
                    config: JSON.stringify({
                        baseUrl: 'http://dev.example.com',
                        timeout: 600000, // 10分钟超时
                        maxConcurrency: 2,
                        retryCount: 2
                    })
                },
                {
                    id: uuidv4(),
                    name: 'Jenkins CI',
                    type: 'jenkins',
                    config: JSON.stringify({
                        jenkinsUrl: 'http://jenkins.example.com',
                        jobName: 'automated-tests',
                        token: '',
                        timeout: 1800000, // 30分钟超时
                        maxConcurrency: 1
                    })
                }
            ];

            environments.forEach(env => {
                db.run(`INSERT INTO execution_environments (
                    id, name, type, config
                ) VALUES (?, ?, ?, ?)`,
                [env.id, env.name, env.type, env.config]);
            });

            console.log('Default execution environments created');
        }
    });
}

// API 路由

// 获取所有项目（支持筛选和搜索）
app.get('/api/projects', (req, res) => {
    const { status, owner, priority, search } = req.query;

    let sql = `
        SELECT p.*,
               COUNT(DISTINCT pm.id) as member_count,
               COUNT(DISTINCT t.id) as testcase_count
        FROM projects p
        LEFT JOIN project_members pm ON p.id = pm.project_id
        LEFT JOIN testcases t ON p.id = t.project_id
        WHERE 1=1
    `;
    const params = [];

    if (status) {
        sql += ' AND p.status = ?';
        params.push(status);
    }
    if (owner) {
        sql += ' AND p.owner = ?';
        params.push(owner);
    }
    if (priority) {
        sql += ' AND p.priority = ?';
        params.push(priority);
    }
    if (search) {
        sql += ' AND (p.name LIKE ? OR p.description LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
    }

    sql += ' GROUP BY p.id ORDER BY p.created_at DESC';

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 解析tags JSON
        const projects = rows.map(row => ({
            ...row,
            tags: row.tags ? JSON.parse(row.tags) : []
        }));

        res.json(projects);
    });
});

// 获取单个项目详情
app.get('/api/projects/:id', (req, res) => {
    const { id } = req.params;

    // 获取项目基本信息
    db.get(`
        SELECT p.*,
               COUNT(DISTINCT pm.id) as member_count,
               COUNT(DISTINCT t.id) as testcase_count
        FROM projects p
        LEFT JOIN project_members pm ON p.id = pm.project_id
        LEFT JOIN testcases t ON p.id = t.project_id
        WHERE p.id = ?
        GROUP BY p.id
    `, [id], (err, project) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!project) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }

        // 获取项目成员
        db.all(`
            SELECT * FROM project_members WHERE project_id = ? ORDER BY joined_at
        `, [id], (err, members) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取项目测试用例统计
            db.all(`
                SELECT status, COUNT(*) as count
                FROM testcases
                WHERE project_id = ?
                GROUP BY status
            `, [id], (err, testcaseStats) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }

                const result = {
                    ...project,
                    tags: project.tags ? JSON.parse(project.tags) : [],
                    members,
                    testcase_stats: testcaseStats
                };

                res.json(result);
            });
        });
    });
});

// 创建项目
app.post('/api/projects', (req, res) => {
    const {
        name, description, owner, start_date, end_date, priority, tags
    } = req.body;

    if (!name) {
        res.status(400).json({ error: 'Project name is required' });
        return;
    }

    const id = uuidv4();
    const tagsJson = JSON.stringify(tags || []);

    db.run(`
        INSERT INTO projects (
            id, name, description, owner, start_date, end_date, priority, tags
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
        id, name, description, owner, start_date, end_date,
        priority || 'medium', tagsJson
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 如果指定了owner，自动添加为项目成员
        if (owner) {
            const memberId = uuidv4();
            db.run(`
                INSERT INTO project_members (id, project_id, user_name, role)
                VALUES (?, ?, ?, ?)
            `, [memberId, id, owner, 'owner']);
        }

        res.json({ id, message: 'Project created successfully' });
    });
});

// 更新项目
app.put('/api/projects/:id', (req, res) => {
    const { id } = req.params;
    const {
        name, description, owner, start_date, end_date, priority, tags, status
    } = req.body;

    const tagsJson = JSON.stringify(tags || []);

    db.run(`
        UPDATE projects SET
            name = ?, description = ?, owner = ?, start_date = ?,
            end_date = ?, priority = ?, tags = ?, status = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `, [
        name, description, owner, start_date, end_date,
        priority, tagsJson, status, id
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Project not found' });
            return;
        }
        res.json({ message: 'Project updated successfully' });
    });
});

// 删除项目
app.delete('/api/projects/:id', (req, res) => {
    const { id } = req.params;

    // 检查项目是否有测试用例
    db.get('SELECT COUNT(*) as count FROM testcases WHERE project_id = ?', [id], (err, result) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (result.count > 0) {
            res.status(400).json({
                error: 'Cannot delete project with existing test cases',
                testcase_count: result.count
            });
            return;
        }

        // 删除项目成员
        db.run('DELETE FROM project_members WHERE project_id = ?', [id], (err) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 删除项目
            db.run('DELETE FROM projects WHERE id = ?', [id], function(err) {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }
                if (this.changes === 0) {
                    res.status(404).json({ error: 'Project not found' });
                    return;
                }
                res.json({ message: 'Project deleted successfully' });
            });
        });
    });
});

// 项目成员管理API

// 添加项目成员
app.post('/api/projects/:id/members', (req, res) => {
    const { id } = req.params;
    const { user_name, role } = req.body;

    if (!user_name) {
        res.status(400).json({ error: 'User name is required' });
        return;
    }

    // 检查成员是否已存在
    db.get(`
        SELECT * FROM project_members
        WHERE project_id = ? AND user_name = ?
    `, [id, user_name], (err, existing) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (existing) {
            res.status(400).json({ error: 'User is already a member of this project' });
            return;
        }

        const memberId = uuidv4();
        db.run(`
            INSERT INTO project_members (id, project_id, user_name, role)
            VALUES (?, ?, ?, ?)
        `, [memberId, id, user_name, role || 'member'], function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: memberId, message: 'Member added successfully' });
        });
    });
});

// 更新项目成员角色
app.put('/api/projects/:id/members/:memberId', (req, res) => {
    const { memberId } = req.params;
    const { role } = req.body;

    db.run(`
        UPDATE project_members SET role = ? WHERE id = ?
    `, [role, memberId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Member not found' });
            return;
        }
        res.json({ message: 'Member role updated successfully' });
    });
});

// 移除项目成员
app.delete('/api/projects/:id/members/:memberId', (req, res) => {
    const { memberId } = req.params;

    db.run('DELETE FROM project_members WHERE id = ?', [memberId], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Member not found' });
            return;
        }
        res.json({ message: 'Member removed successfully' });
    });
});

// 获取所有测试用例
app.get('/api/testcases', (req, res) => {
    const { project_id, status, priority, creator, search } = req.query;
    
    let sql = `
        SELECT t.*, p.name as project_name 
        FROM testcases t 
        LEFT JOIN projects p ON t.project_id = p.id 
        WHERE 1=1
    `;
    const params = [];

    if (project_id) {
        sql += ' AND t.project_id = ?';
        params.push(project_id);
    }
    if (status) {
        sql += ' AND t.status = ?';
        params.push(status);
    }
    if (priority) {
        sql += ' AND t.priority = ?';
        params.push(priority);
    }
    if (creator) {
        sql += ' AND t.creator = ?';
        params.push(creator);
    }
    if (search) {
        sql += ' AND (t.name LIKE ? OR t.description LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
    }

    sql += ' ORDER BY t.created_at DESC';

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        
        // 解析步骤JSON
        const testcases = rows.map(row => ({
            ...row,
            steps: row.steps ? JSON.parse(row.steps) : []
        }));
        
        res.json(testcases);
    });
});

// 获取单个测试用例
app.get('/api/testcases/:id', (req, res) => {
    const { id } = req.params;
    
    db.get(`
        SELECT t.*, p.name as project_name 
        FROM testcases t 
        LEFT JOIN projects p ON t.project_id = p.id 
        WHERE t.id = ?
    `, [id], (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (!row) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        
        // 解析步骤JSON
        const testcase = {
            ...row,
            steps: row.steps ? JSON.parse(row.steps) : []
        };
        
        res.json(testcase);
    });
});

// 创建测试用例
app.post('/api/testcases', (req, res) => {
    const {
        name, description, project_id, priority, type, execution_type,
        precondition, steps, expected_result, creator
    } = req.body;

    if (!name || !project_id) {
        res.status(400).json({ error: 'Name and project_id are required' });
        return;
    }

    const id = uuidv4();
    const stepsJson = JSON.stringify(steps || []);

    db.run(`
        INSERT INTO testcases (
            id, name, description, project_id, priority, type, execution_type,
            precondition, steps, expected_result, creator
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
        id, name, description, project_id, priority || 'medium', 
        type || 'functional', execution_type || 'manual',
        precondition, stepsJson, expected_result, creator
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id, message: 'Test case created successfully' });
    });
});

// 更新测试用例
app.put('/api/testcases/:id', (req, res) => {
    const { id } = req.params;
    const {
        name, description, project_id, priority, type, execution_type,
        precondition, steps, expected_result, status
    } = req.body;

    const stepsJson = JSON.stringify(steps || []);

    db.run(`
        UPDATE testcases SET 
            name = ?, description = ?, project_id = ?, priority = ?, 
            type = ?, execution_type = ?, precondition = ?, steps = ?, 
            expected_result = ?, status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    `, [
        name, description, project_id, priority, type, execution_type,
        precondition, stepsJson, expected_result, status, id
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        res.json({ message: 'Test case updated successfully' });
    });
});

// 删除测试用例
app.delete('/api/testcases/:id', (req, res) => {
    const { id } = req.params;

    db.run('DELETE FROM testcases WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        if (this.changes === 0) {
            res.status(404).json({ error: 'Test case not found' });
            return;
        }
        res.json({ message: 'Test case deleted successfully' });
    });
});

// 批量删除测试用例
app.delete('/api/testcases', (req, res) => {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
        res.status(400).json({ error: 'IDs array is required' });
        return;
    }

    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM testcases WHERE id IN (${placeholders})`;

    db.run(sql, ids, function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ 
            message: `${this.changes} test case(s) deleted successfully`,
            deletedCount: this.changes 
        });
    });
});

// 执行测试用例
app.post('/api/testcases/:id/execute', (req, res) => {
    const { id } = req.params;
    const { environment, notes, executor } = req.body;

    const executionId = uuidv4();
    const status = 'running';

    // 创建执行记录
    db.run(`
        INSERT INTO test_executions (id, testcase_id, environment, status, notes, executor)
        VALUES (?, ?, ?, ?, ?, ?)
    `, [executionId, id, environment, status, notes, executor], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 更新测试用例的最后执行时间
        db.run(`
            UPDATE testcases SET last_execution = CURRENT_TIMESTAMP WHERE id = ?
        `, [id]);

        res.json({ 
            executionId,
            message: 'Test case execution started',
            status: 'running'
        });
    });
});

// 任务队列系统
class TestExecutionQueue {
    constructor() {
        this.queue = [];
        this.running = new Map(); // 正在执行的任务
        this.maxConcurrency = 3;
    }

    addTask(execution) {
        this.queue.push(execution);
        this.processQueue();
    }

    async processQueue() {
        if (this.running.size >= this.maxConcurrency || this.queue.length === 0) {
            return;
        }

        const execution = this.queue.shift();
        this.running.set(execution.id, execution);

        try {
            await this.executeTest(execution);
        } catch (error) {
            console.error('Test execution error:', error);
            this.updateExecutionStatus(execution.id, 'failed', error.message);
        } finally {
            this.running.delete(execution.id);
            this.processQueue(); // 处理下一个任务
        }
    }

    async executeTest(execution) {
        console.log(`Starting test execution: ${execution.id}`);

        // 更新状态为运行中
        this.updateExecutionStatus(execution.id, 'running');

        // 广播执行开始
        this.broadcastExecutionUpdate(execution.id, {
            status: 'running',
            message: '测试执行开始'
        });

        // 模拟测试执行过程
        const testcaseIds = JSON.parse(execution.testcase_ids);
        const totalCount = testcaseIds.length;
        let passedCount = 0;
        let failedCount = 0;

        for (let i = 0; i < testcaseIds.length; i++) {
            const testcaseId = testcaseIds[i];

            // 更新当前执行的测试用例
            const progress = Math.round(((i + 1) / totalCount) * 100);
            this.updateExecutionProgress(execution.id, progress, testcaseId);

            // 广播进度更新
            this.broadcastExecutionUpdate(execution.id, {
                status: 'running',
                progress: progress,
                currentTestcase: testcaseId,
                message: `正在执行测试用例 ${i + 1}/${totalCount}`
            });

            // 模拟测试用例执行
            const result = await this.executeTestcase(execution.id, testcaseId);

            if (result.status === 'passed') {
                passedCount++;
            } else {
                failedCount++;
            }

            // 短暂延迟模拟真实执行时间
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // 执行完成
        const finalStatus = failedCount === 0 ? 'completed' : 'failed';
        this.updateExecutionCounts(execution.id, totalCount, passedCount, failedCount, 0);
        this.updateExecutionStatus(execution.id, finalStatus);

        this.broadcastExecutionUpdate(execution.id, {
            status: finalStatus,
            progress: 100,
            message: `测试执行完成 - 通过: ${passedCount}, 失败: ${failedCount}`,
            totalCount,
            passedCount,
            failedCount
        });
    }

    async executeTestcase(executionId, testcaseId) {
        // 模拟测试用例执行
        const startTime = new Date();

        // 随机决定测试结果（80%通过率）
        const passed = Math.random() > 0.2;
        const status = passed ? 'passed' : 'failed';
        const errorMessage = passed ? null : '断言失败：期望值与实际值不匹配';

        const endTime = new Date();
        const duration = endTime - startTime;

        // 保存测试用例执行结果
        const resultId = uuidv4();
        db.run(`INSERT INTO testcase_execution_results (
            id, execution_id, testcase_id, status, start_time, end_time, duration, error_message
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            resultId, executionId, testcaseId, status, startTime.toISOString(),
            endTime.toISOString(), duration, errorMessage
        ]);

        // 记录执行日志
        this.addExecutionLog(executionId, testcaseId, 'info',
            `测试用例 ${testcaseId} 执行${status === 'passed' ? '成功' : '失败'}`);

        if (errorMessage) {
            this.addExecutionLog(executionId, testcaseId, 'error', errorMessage);
        }

        return { status, errorMessage, duration };
    }

    updateExecutionStatus(executionId, status, errorMessage = null) {
        const updateTime = new Date().toISOString();
        let sql = `UPDATE test_executions SET status = ?, updated_at = ?`;
        let params = [status, updateTime];

        if (status === 'running') {
            sql += `, start_time = ?`;
            params.push(updateTime);
        } else if (status === 'completed' || status === 'failed') {
            sql += `, end_time = ?`;
            params.push(updateTime);
        }

        sql += ` WHERE id = ?`;
        params.push(executionId);

        db.run(sql, params);

        if (errorMessage) {
            this.addExecutionLog(executionId, null, 'error', errorMessage);
        }
    }

    updateExecutionProgress(executionId, progress, currentTestcaseId) {
        db.run(`UPDATE test_executions SET progress = ?, current_testcase_id = ? WHERE id = ?`,
            [progress, currentTestcaseId, executionId]);
    }

    updateExecutionCounts(executionId, totalCount, passedCount, failedCount, skippedCount) {
        db.run(`UPDATE test_executions SET
            total_count = ?, passed_count = ?, failed_count = ?, skipped_count = ?
            WHERE id = ?`,
            [totalCount, passedCount, failedCount, skippedCount, executionId]);
    }

    addExecutionLog(executionId, testcaseId, level, message) {
        const logId = uuidv4();
        db.run(`INSERT INTO test_execution_logs (
            id, execution_id, testcase_id, log_level, message
        ) VALUES (?, ?, ?, ?, ?)`, [
            logId, executionId, testcaseId, level, message
        ]);

        // 广播日志更新
        this.broadcastLogUpdate(executionId, {
            id: logId,
            testcaseId,
            level,
            message,
            timestamp: new Date().toISOString()
        });
    }

    broadcastExecutionUpdate(executionId, data) {
        const message = JSON.stringify({
            type: 'execution_update',
            executionId,
            data
        });

        if (typeof wss !== 'undefined') {
            wss.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.send(message);
                }
            });
        }
    }

    broadcastLogUpdate(executionId, logData) {
        const message = JSON.stringify({
            type: 'log_update',
            executionId,
            data: logData
        });

        if (typeof wss !== 'undefined') {
            wss.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.send(message);
                }
            });
        }
    }

    cancelExecution(executionId) {
        // 从队列中移除
        this.queue = this.queue.filter(exec => exec.id !== executionId);

        // 如果正在运行，标记为取消
        if (this.running.has(executionId)) {
            this.updateExecutionStatus(executionId, 'cancelled');
            this.running.delete(executionId);

            this.broadcastExecutionUpdate(executionId, {
                status: 'cancelled',
                message: '测试执行已取消'
            });
        }
    }
}

// 创建全局任务队列实例
const executionQueue = new TestExecutionQueue();

// 测试执行相关API

// 获取执行环境列表
app.get('/api/execution-environments', (req, res) => {
    db.all(`SELECT * FROM execution_environments WHERE is_active = 1 ORDER BY name`, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const environments = rows.map(row => ({
            ...row,
            config: JSON.parse(row.config || '{}')
        }));

        res.json(environments);
    });
});

// 创建测试套件
app.post('/api/test-suites', (req, res) => {
    const { name, description, project_id, testcase_ids, created_by } = req.body;

    if (!name || !project_id || !testcase_ids || testcase_ids.length === 0) {
        res.status(400).json({ error: 'Name, project_id and testcase_ids are required' });
        return;
    }

    const id = uuidv4();
    const testcaseIdsJson = JSON.stringify(testcase_ids);

    db.run(`INSERT INTO test_suites (
        id, name, description, project_id, testcase_ids, created_by
    ) VALUES (?, ?, ?, ?, ?, ?)`, [
        id, name, description, project_id, testcaseIdsJson, created_by
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ id, message: 'Test suite created successfully' });
    });
});

// 获取测试套件列表
app.get('/api/test-suites', (req, res) => {
    const { project_id } = req.query;

    let sql = `SELECT * FROM test_suites`;
    let params = [];

    if (project_id) {
        sql += ` WHERE project_id = ?`;
        params.push(project_id);
    }

    sql += ` ORDER BY created_at DESC`;

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const suites = rows.map(row => ({
            ...row,
            testcase_ids: JSON.parse(row.testcase_ids || '[]')
        }));

        res.json(suites);
    });
});

// 启动测试执行
app.post('/api/test-executions', (req, res) => {
    const {
        project_id,
        suite_id,
        testcase_ids,
        environment_id,
        executor
    } = req.body;

    if (!project_id || !testcase_ids || testcase_ids.length === 0 || !environment_id) {
        res.status(400).json({
            error: 'project_id, testcase_ids and environment_id are required'
        });
        return;
    }

    const executionId = uuidv4();
    const testcaseIdsJson = JSON.stringify(testcase_ids);

    // 创建执行记录
    db.run(`INSERT INTO test_executions (
        id, project_id, suite_id, testcase_ids, environment_id,
        status, total_count, executor
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
        executionId, project_id, suite_id, testcaseIdsJson,
        environment_id, 'pending', testcase_ids.length, executor
    ], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        // 添加到执行队列
        const execution = {
            id: executionId,
            project_id,
            suite_id,
            testcase_ids: testcaseIdsJson,
            environment_id,
            executor
        };

        executionQueue.addTask(execution);

        res.json({
            id: executionId,
            message: 'Test execution started',
            status: 'pending'
        });
    });
});

// 获取测试执行列表
app.get('/api/test-executions', (req, res) => {
    const { project_id, status, limit = 50 } = req.query;

    let sql = `
        SELECT e.*, p.name as project_name, s.name as suite_name, env.name as environment_name
        FROM test_executions e
        LEFT JOIN projects p ON e.project_id = p.id
        LEFT JOIN test_suites s ON e.suite_id = s.id
        LEFT JOIN execution_environments env ON e.environment_id = env.id
        WHERE 1=1
    `;
    let params = [];

    if (project_id) {
        sql += ` AND e.project_id = ?`;
        params.push(project_id);
    }

    if (status) {
        sql += ` AND e.status = ?`;
        params.push(status);
    }

    sql += ` ORDER BY e.created_at DESC LIMIT ?`;
    params.push(parseInt(limit));

    db.all(sql, params, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        const executions = rows.map(row => ({
            ...row,
            testcase_ids: JSON.parse(row.testcase_ids || '[]')
        }));

        res.json(executions);
    });
});

// 获取单个测试执行详情
app.get('/api/test-executions/:id', (req, res) => {
    const { id } = req.params;

    db.get(`
        SELECT e.*, p.name as project_name, s.name as suite_name, env.name as environment_name
        FROM test_executions e
        LEFT JOIN projects p ON e.project_id = p.id
        LEFT JOIN test_suites s ON e.suite_id = s.id
        LEFT JOIN execution_environments env ON e.environment_id = env.id
        WHERE e.id = ?
    `, [id], (err, execution) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (!execution) {
            res.status(404).json({ error: 'Execution not found' });
            return;
        }

        // 获取执行结果
        db.all(`
            SELECT r.*, t.name as testcase_name
            FROM testcase_execution_results r
            LEFT JOIN testcases t ON r.testcase_id = t.id
            WHERE r.execution_id = ?
            ORDER BY r.created_at
        `, [id], (err, results) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // 获取执行日志
            db.all(`
                SELECT * FROM test_execution_logs
                WHERE execution_id = ?
                ORDER BY timestamp
            `, [id], (err, logs) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }

                res.json({
                    ...execution,
                    testcase_ids: JSON.parse(execution.testcase_ids || '[]'),
                    results,
                    logs
                });
            });
        });
    });
});

// 取消测试执行
app.post('/api/test-executions/:id/cancel', (req, res) => {
    const { id } = req.params;

    // 检查执行是否存在且可以取消
    db.get(`SELECT * FROM test_executions WHERE id = ?`, [id], (err, execution) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }

        if (!execution) {
            res.status(404).json({ error: 'Execution not found' });
            return;
        }

        if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
            res.status(400).json({ error: 'Cannot cancel completed execution' });
            return;
        }

        // 取消执行
        executionQueue.cancelExecution(id);

        res.json({ message: 'Execution cancelled successfully' });
    });
});

// 获取执行日志
app.get('/api/test-executions/:id/logs', (req, res) => {
    const { id } = req.params;
    const { level, limit = 1000 } = req.query;

    let sql = `SELECT * FROM test_execution_logs WHERE execution_id = ?`;
    let params = [id];

    if (level) {
        sql += ` AND log_level = ?`;
        params.push(level);
    }

    sql += ` ORDER BY timestamp DESC LIMIT ?`;
    params.push(parseInt(limit));

    db.all(sql, params, (err, logs) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(logs);
    });
});

// 静态文件服务
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 创建HTTP服务器和WebSocket服务器
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// WebSocket连接处理
wss.on('connection', (ws) => {
    console.log('WebSocket client connected');

    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log('Received WebSocket message:', data);

            // 处理客户端消息
            if (data.type === 'subscribe_execution') {
                // 客户端订阅特定执行的更新
                ws.executionId = data.executionId;
            }
        } catch (error) {
            console.error('WebSocket message error:', error);
        }
    });

    ws.on('close', () => {
        console.log('WebSocket client disconnected');
    });
});

// 启动服务器
server.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log(`WebSocket server is running on ws://localhost:${PORT}`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    db.close((err) => {
        if (err) {
            console.error(err.message);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});
