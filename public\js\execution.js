// 测试执行模块
console.log('测试执行模块已加载');

let currentExecution = null;
let websocket = null;
let allProjects = [];
let allTestcases = [];
let allEnvironments = [];
let allExecutions = [];

// 加载测试执行页面
async function loadExecutionPage() {
    console.log('loadExecutionPage函数被调用');
    const container = document.getElementById('execution-page');
    
    if (!container) {
        console.error('找不到execution-page容器');
        return;
    }
    
    // 显示加载状态
    container.innerHTML = `
        <div class="flex justify-center items-center h-64">
            <div class="loading"></div>
            <span class="ml-2">加载测试执行...</span>
        </div>
    `;

    try {
        // 加载必要数据
        await Promise.all([
            loadProjects(),
            loadEnvironments(),
            loadExecutions()
        ]);
        
        // 渲染测试执行页面
        renderExecutionPage();
        
        // 初始化WebSocket连接
        initWebSocket();
        
    } catch (error) {
        console.error('Error loading execution page:', error);
        container.innerHTML = `
            <div class="text-center text-red-600 p-8">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>加载测试执行页面失败</p>
                <button onclick="loadExecutionPage()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    重试
                </button>
            </div>
        `;
    }
}

async function loadProjects() {
    const response = await fetch(`${API_BASE}/projects`);
    if (!response.ok) throw new Error('Failed to load projects');
    allProjects = await response.json();
}

async function loadEnvironments() {
    const response = await fetch(`${API_BASE}/execution-environments`);
    if (!response.ok) throw new Error('Failed to load environments');
    allEnvironments = await response.json();
}

async function loadExecutions() {
    const response = await fetch(`${API_BASE}/test-executions?limit=20`);
    if (!response.ok) throw new Error('Failed to load executions');
    allExecutions = await response.json();
}

function renderExecutionPage() {
    const container = document.getElementById('execution-page');
    
    container.innerHTML = `
        <div class="space-y-6">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-bold text-gray-800">测试执行</h2>
                <button onclick="openNewExecutionModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-play mr-2"></i> 新建执行
                </button>
            </div>

            <!-- 执行统计 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-full mr-4">
                            <i class="fas fa-play text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">总执行次数</p>
                            <h3 class="text-2xl font-bold">${allExecutions.length}</h3>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-full mr-4">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">成功执行</p>
                            <h3 class="text-2xl font-bold text-green-600">${allExecutions.filter(e => e.status === 'completed').length}</h3>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="bg-red-100 p-3 rounded-full mr-4">
                            <i class="fas fa-times-circle text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">失败执行</p>
                            <h3 class="text-2xl font-bold text-red-600">${allExecutions.filter(e => e.status === 'failed').length}</h3>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 p-3 rounded-full mr-4">
                            <i class="fas fa-clock text-yellow-600 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-gray-500 text-sm">运行中</p>
                            <h3 class="text-2xl font-bold text-yellow-600">${allExecutions.filter(e => e.status === 'running' || e.status === 'pending').length}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 执行历史 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">执行历史</h3>
                        <div class="flex space-x-2">
                            <select id="filter-project" class="border rounded px-3 py-1 text-sm">
                                <option value="">全部项目</option>
                                ${allProjects.map(p => `<option value="${p.id}">${p.name}</option>`).join('')}
                            </select>
                            <select id="filter-status" class="border rounded px-3 py-1 text-sm">
                                <option value="">全部状态</option>
                                <option value="pending">等待中</option>
                                <option value="running">运行中</option>
                                <option value="completed">已完成</option>
                                <option value="failed">失败</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <button onclick="refreshExecutions()" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-refresh mr-1"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div id="executions-list" class="p-6">
                    ${renderExecutionsList()}
                </div>
            </div>
        </div>
    `;

    // 绑定筛选事件
    document.getElementById('filter-project').addEventListener('change', filterExecutions);
    document.getElementById('filter-status').addEventListener('change', filterExecutions);
}

function renderExecutionsList() {
    if (allExecutions.length === 0) {
        return `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-play-circle text-4xl mb-4"></i>
                <p class="mb-4">暂无执行记录</p>
                <button onclick="openNewExecutionModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    开始第一次执行
                </button>
            </div>
        `;
    }

    return `
        <div class="space-y-4">
            ${allExecutions.map(execution => `
                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h4 class="font-semibold text-gray-900">${execution.project_name}</h4>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full ${getExecutionStatusClass(execution.status)}">
                                    ${getExecutionStatusText(execution.status)}
                                </span>
                                ${execution.status === 'running' ? `
                                    <div class="flex items-center text-sm text-blue-600">
                                        <div class="loading w-4 h-4 mr-1"></div>
                                        <span>${execution.progress || 0}%</span>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="text-sm text-gray-600 space-y-1">
                                <div>执行环境: ${execution.environment_name}</div>
                                <div>测试用例: ${execution.total_count} 个</div>
                                ${execution.status === 'completed' || execution.status === 'failed' ? `
                                    <div>结果: 通过 ${execution.passed_count || 0} / 失败 ${execution.failed_count || 0}</div>
                                ` : ''}
                                <div>执行者: ${execution.executor || '未知'}</div>
                                <div>创建时间: ${formatDate(execution.created_at)}</div>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="viewExecutionDetail('${execution.id}')" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            ${execution.status === 'running' || execution.status === 'pending' ? `
                                <button onclick="cancelExecution('${execution.id}')" class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-stop"></i> 取消
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function getExecutionStatusClass(status) {
    switch(status) {
        case 'pending': return 'bg-gray-100 text-gray-800';
        case 'running': return 'bg-blue-100 text-blue-800';
        case 'completed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        case 'cancelled': return 'bg-yellow-100 text-yellow-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getExecutionStatusText(status) {
    switch(status) {
        case 'pending': return '等待中';
        case 'running': return '运行中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        case 'cancelled': return '已取消';
        default: return '未知';
    }
}

async function filterExecutions() {
    const projectId = document.getElementById('filter-project').value;
    const status = document.getElementById('filter-status').value;
    
    const params = new URLSearchParams();
    if (projectId) params.append('project_id', projectId);
    if (status) params.append('status', status);
    params.append('limit', '20');
    
    try {
        const response = await fetch(`${API_BASE}/test-executions?${params}`);
        if (!response.ok) throw new Error('Failed to filter executions');
        
        allExecutions = await response.json();
        
        // 更新执行列表
        const container = document.getElementById('executions-list');
        container.innerHTML = renderExecutionsList();
        
    } catch (error) {
        console.error('Error filtering executions:', error);
        showNotification('筛选执行记录失败', 'error');
    }
}

async function refreshExecutions() {
    try {
        await loadExecutions();
        
        // 更新统计和列表
        renderExecutionPage();
        
        showNotification('执行记录已刷新', 'success');
    } catch (error) {
        console.error('Error refreshing executions:', error);
        showNotification('刷新执行记录失败', 'error');
    }
}

// WebSocket连接管理
function initWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}`;
    
    websocket = new WebSocket(wsUrl);
    
    websocket.onopen = () => {
        console.log('WebSocket连接已建立');
    };
    
    websocket.onmessage = (event) => {
        try {
            const message = JSON.parse(event.data);
            handleWebSocketMessage(message);
        } catch (error) {
            console.error('WebSocket消息解析错误:', error);
        }
    };
    
    websocket.onclose = () => {
        console.log('WebSocket连接已关闭');
        // 5秒后尝试重连
        setTimeout(initWebSocket, 5000);
    };
    
    websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
    };
}

function handleWebSocketMessage(message) {
    console.log('收到WebSocket消息:', message);
    
    if (message.type === 'execution_update') {
        updateExecutionStatus(message.executionId, message.data);
    } else if (message.type === 'log_update') {
        updateExecutionLogs(message.executionId, message.data);
    }
}

function updateExecutionStatus(executionId, data) {
    // 更新执行列表中的状态
    const execution = allExecutions.find(e => e.id === executionId);
    if (execution) {
        execution.status = data.status;
        execution.progress = data.progress;
        execution.passed_count = data.passedCount;
        execution.failed_count = data.failedCount;
        execution.total_count = data.totalCount;
        
        // 重新渲染执行列表
        const container = document.getElementById('executions-list');
        if (container) {
            container.innerHTML = renderExecutionsList();
        }
    }
    
    // 如果正在查看详情，更新详情页面
    if (currentExecution && currentExecution.id === executionId) {
        updateExecutionDetailStatus(data);
    }
    
    // 显示通知
    if (data.message) {
        const type = data.status === 'failed' ? 'error' : 
                    data.status === 'completed' ? 'success' : 'info';
        showNotification(data.message, type);
    }
}

// 新建执行模态框
function openNewExecutionModal() {
    showExecutionModal('新建测试执行', {});
}

function showExecutionModal(title, execution = {}) {
    const modalHtml = `
        <div id="execution-form-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        <button onclick="closeExecutionModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="execution-form" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择项目 *</label>
                                <select id="execution-project" required class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择项目</option>
                                    ${allProjects.map(p => `<option value="${p.id}">${p.name}</option>`).join('')}
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行环境 *</label>
                                <select id="execution-environment" required class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择执行环境</option>
                                    ${allEnvironments.map(e => `<option value="${e.id}">${e.name} (${e.type})</option>`).join('')}
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">执行者</label>
                            <input id="execution-executor" type="text" value="${execution.executor || ''}"
                                   class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入执行者姓名">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择测试用例 *</label>
                            <div id="testcase-selection" class="border rounded-lg p-4 max-h-64 overflow-y-auto">
                                <p class="text-gray-500">请先选择项目</p>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeExecutionModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-play mr-1"></i> 开始执行
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定事件
    const form = document.getElementById('execution-form');
    form.addEventListener('submit', handleExecutionFormSubmit);

    const projectSelect = document.getElementById('execution-project');
    projectSelect.addEventListener('change', loadProjectTestcases);
}

function closeExecutionModal() {
    const modal = document.getElementById('execution-form-modal');
    if (modal) {
        modal.remove();
    }
}

async function loadProjectTestcases() {
    const projectId = document.getElementById('execution-project').value;
    const container = document.getElementById('testcase-selection');

    if (!projectId) {
        container.innerHTML = '<p class="text-gray-500">请先选择项目</p>';
        return;
    }

    try {
        container.innerHTML = '<div class="text-center"><div class="loading mx-auto mb-2"></div><p>加载测试用例...</p></div>';

        const response = await fetch(`${API_BASE}/testcases?project_id=${projectId}`);
        if (!response.ok) throw new Error('Failed to load testcases');

        const testcases = await response.json();

        if (testcases.length === 0) {
            container.innerHTML = '<p class="text-gray-500">该项目暂无测试用例</p>';
            return;
        }

        container.innerHTML = `
            <div class="space-y-2">
                <div class="flex items-center mb-3">
                    <input type="checkbox" id="select-all-testcases" class="mr-2 rounded">
                    <label for="select-all-testcases" class="font-medium">全选 (${testcases.length} 个用例)</label>
                </div>
                ${testcases.map(tc => `
                    <div class="flex items-center">
                        <input type="checkbox" id="testcase-${tc.id}" name="testcase" value="${tc.id}" class="mr-2 rounded">
                        <label for="testcase-${tc.id}" class="flex-1 text-sm">
                            <span class="font-medium">${tc.name}</span>
                            <span class="text-gray-500 ml-2">(${tc.type} - ${tc.priority})</span>
                        </label>
                    </div>
                `).join('')}
            </div>
        `;

        // 绑定全选事件
        document.getElementById('select-all-testcases').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="testcase"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

    } catch (error) {
        console.error('Error loading testcases:', error);
        container.innerHTML = '<p class="text-red-500">加载测试用例失败</p>';
    }
}

async function handleExecutionFormSubmit(e) {
    e.preventDefault();

    const formData = {
        project_id: document.getElementById('execution-project').value,
        environment_id: document.getElementById('execution-environment').value,
        executor: document.getElementById('execution-executor').value.trim(),
        testcase_ids: Array.from(document.querySelectorAll('input[name="testcase"]:checked')).map(cb => cb.value)
    };

    // 验证
    if (!formData.project_id) {
        showNotification('请选择项目', 'error');
        return;
    }

    if (!formData.environment_id) {
        showNotification('请选择执行环境', 'error');
        return;
    }

    if (formData.testcase_ids.length === 0) {
        showNotification('请至少选择一个测试用例', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/test-executions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error('Failed to start execution');
        }

        const result = await response.json();

        showNotification('测试执行已启动', 'success');
        closeExecutionModal();

        // 刷新执行列表
        await refreshExecutions();

        // 自动打开执行详情
        setTimeout(() => {
            viewExecutionDetail(result.id);
        }, 1000);

    } catch (error) {
        console.error('Error starting execution:', error);
        showNotification('启动测试执行失败', 'error');
    }
}

// 查看执行详情
async function viewExecutionDetail(executionId) {
    try {
        const response = await fetch(`${API_BASE}/test-executions/${executionId}`);
        if (!response.ok) throw new Error('Failed to load execution detail');

        const execution = await response.json();
        currentExecution = execution;

        showExecutionDetailModal(execution);

        // 订阅WebSocket更新
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
                type: 'subscribe_execution',
                executionId: executionId
            }));
        }

    } catch (error) {
        console.error('Error loading execution detail:', error);
        showNotification('加载执行详情失败', 'error');
    }
}

// 取消执行
async function cancelExecution(executionId) {
    if (!confirm('确定要取消这个测试执行吗？')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/test-executions/${executionId}/cancel`, {
            method: 'POST'
        });

        if (!response.ok) {
            throw new Error('Failed to cancel execution');
        }

        showNotification('测试执行已取消', 'success');

        // 刷新执行列表
        await refreshExecutions();

    } catch (error) {
        console.error('Error cancelling execution:', error);
        showNotification('取消执行失败', 'error');
    }
}

// 执行详情模态框
function showExecutionDetailModal(execution) {
    const modalHtml = `
        <div id="execution-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-medium text-gray-900">执行详情</h3>
                        <button onclick="closeExecutionDetailModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- 执行基本信息 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">${execution.project_name}</h4>
                                <div class="space-y-1 text-sm">
                                    <div><span class="text-gray-500">执行环境:</span> ${execution.environment_name}</div>
                                    <div><span class="text-gray-500">执行者:</span> ${execution.executor || '未知'}</div>
                                    <div><span class="text-gray-500">创建时间:</span> ${formatDate(execution.created_at)}</div>
                                </div>
                            </div>
                            <div>
                                <div class="flex items-center mb-2">
                                    <span class="px-3 py-1 text-sm font-semibold rounded-full ${getExecutionStatusClass(execution.status)}">
                                        ${getExecutionStatusText(execution.status)}
                                    </span>
                                    ${execution.status === 'running' ? `
                                        <div class="ml-3 flex items-center text-sm text-blue-600">
                                            <div class="loading w-4 h-4 mr-1"></div>
                                            <span id="execution-progress">${execution.progress || 0}%</span>
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="space-y-1 text-sm">
                                    <div><span class="text-gray-500">总用例:</span> ${execution.total_count}</div>
                                    <div><span class="text-gray-500">已通过:</span> <span class="text-green-600">${execution.passed_count || 0}</span></div>
                                    <div><span class="text-gray-500">已失败:</span> <span class="text-red-600">${execution.failed_count || 0}</span></div>
                                </div>
                            </div>
                            <div>
                                <div class="space-y-1 text-sm">
                                    <div><span class="text-gray-500">开始时间:</span> ${execution.start_time ? formatDate(execution.start_time) : '未开始'}</div>
                                    <div><span class="text-gray-500">结束时间:</span> ${execution.end_time ? formatDate(execution.end_time) : '未结束'}</div>
                                    <div><span class="text-gray-500">执行时长:</span> ${execution.duration ? formatDuration(execution.duration) : '计算中...'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页 -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="switchExecutionTab('results')" id="tab-results" class="execution-tab active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                测试结果
                            </button>
                            <button onclick="switchExecutionTab('logs')" id="tab-logs" class="execution-tab py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                执行日志
                            </button>
                        </nav>
                    </div>

                    <!-- 标签页内容 -->
                    <div id="execution-tab-content">
                        ${renderExecutionResults(execution)}
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        ${execution.status === 'running' || execution.status === 'pending' ? `
                            <button onclick="cancelExecution('${execution.id}')" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                <i class="fas fa-stop mr-1"></i> 取消执行
                            </button>
                        ` : ''}
                        <button onclick="closeExecutionDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeExecutionDetailModal() {
    const modal = document.getElementById('execution-detail-modal');
    if (modal) {
        modal.remove();
    }
    currentExecution = null;
}

function switchExecutionTab(tabName) {
    // 更新标签样式
    document.querySelectorAll('.execution-tab').forEach(tab => {
        tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });

    const activeTab = document.getElementById(`tab-${tabName}`);
    activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeTab.classList.remove('border-transparent', 'text-gray-500');

    // 更新内容
    const content = document.getElementById('execution-tab-content');
    if (tabName === 'results') {
        content.innerHTML = renderExecutionResults(currentExecution);
    } else if (tabName === 'logs') {
        content.innerHTML = renderExecutionLogs(currentExecution);
        loadExecutionLogs(currentExecution.id);
    }
}

function renderExecutionResults(execution) {
    if (!execution.results || execution.results.length === 0) {
        return `
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                <p>暂无测试结果</p>
            </div>
        `;
    }

    return `
        <div class="space-y-4">
            ${execution.results.map(result => `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h5 class="font-medium text-gray-900">${result.testcase_name}</h5>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full ${getTestResultStatusClass(result.status)}">
                                    ${getTestResultStatusText(result.status)}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600">
                                <div>执行时长: ${result.duration ? formatDuration(result.duration) : '未知'}</div>
                                <div>开始时间: ${result.start_time ? formatDate(result.start_time) : '未知'}</div>
                            </div>
                            ${result.error_message ? `
                                <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                                    <strong>错误信息:</strong> ${result.error_message}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function renderExecutionLogs(execution) {
    return `
        <div id="execution-logs-container" class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
            <div class="text-center text-gray-500">
                <div class="loading mx-auto mb-2"></div>
                <p>加载执行日志...</p>
            </div>
        </div>
    `;
}

async function loadExecutionLogs(executionId) {
    try {
        const response = await fetch(`${API_BASE}/test-executions/${executionId}/logs`);
        if (!response.ok) throw new Error('Failed to load logs');

        const logs = await response.json();

        const container = document.getElementById('execution-logs-container');
        if (!container) return;

        if (logs.length === 0) {
            container.innerHTML = '<div class="text-center text-gray-500">暂无执行日志</div>';
            return;
        }

        container.innerHTML = logs.reverse().map(log => `
            <div class="mb-1">
                <span class="text-gray-400">[${formatTime(log.timestamp)}]</span>
                <span class="text-${getLogLevelColor(log.log_level)}-400">[${log.log_level.toUpperCase()}]</span>
                <span>${log.message}</span>
            </div>
        `).join('');

        // 滚动到底部
        container.scrollTop = container.scrollHeight;

    } catch (error) {
        console.error('Error loading logs:', error);
        const container = document.getElementById('execution-logs-container');
        if (container) {
            container.innerHTML = '<div class="text-center text-red-500">加载日志失败</div>';
        }
    }
}

function getTestResultStatusClass(status) {
    switch(status) {
        case 'passed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        case 'skipped': return 'bg-yellow-100 text-yellow-800';
        case 'error': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getTestResultStatusText(status) {
    switch(status) {
        case 'passed': return '通过';
        case 'failed': return '失败';
        case 'skipped': return '跳过';
        case 'error': return '错误';
        default: return '未知';
    }
}

function getLogLevelColor(level) {
    switch(level) {
        case 'error': return 'red';
        case 'warn': return 'yellow';
        case 'info': return 'blue';
        case 'debug': return 'gray';
        default: return 'green';
    }
}

function formatDuration(milliseconds) {
    if (!milliseconds) return '0ms';

    if (milliseconds < 1000) {
        return `${milliseconds}ms`;
    } else if (milliseconds < 60000) {
        return `${(milliseconds / 1000).toFixed(1)}s`;
    } else {
        const minutes = Math.floor(milliseconds / 60000);
        const seconds = Math.floor((milliseconds % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function updateExecutionDetailStatus(data) {
    // 更新进度
    const progressElement = document.getElementById('execution-progress');
    if (progressElement && data.progress !== undefined) {
        progressElement.textContent = `${data.progress}%`;
    }

    // 如果正在查看日志标签，实时更新日志
    const activeTab = document.querySelector('.execution-tab.active');
    if (activeTab && activeTab.id === 'tab-logs') {
        // 重新加载日志
        loadExecutionLogs(currentExecution.id);
    }
}

function updateExecutionLogs(executionId, logData) {
    // 如果正在查看该执行的日志，实时添加新日志
    if (currentExecution && currentExecution.id === executionId) {
        const container = document.getElementById('execution-logs-container');
        if (container && !container.querySelector('.text-center')) {
            const logElement = document.createElement('div');
            logElement.className = 'mb-1';
            logElement.innerHTML = `
                <span class="text-gray-400">[${formatTime(logData.timestamp)}]</span>
                <span class="text-${getLogLevelColor(logData.level)}-400">[${logData.level.toUpperCase()}]</span>
                <span>${logData.message}</span>
            `;
            container.appendChild(logElement);

            // 滚动到底部
            container.scrollTop = container.scrollHeight;
        }
    }
}

// 全局函数
window.loadExecutionPage = loadExecutionPage;
window.openNewExecutionModal = openNewExecutionModal;
window.closeExecutionModal = closeExecutionModal;
window.viewExecutionDetail = viewExecutionDetail;
window.closeExecutionDetailModal = closeExecutionDetailModal;
window.switchExecutionTab = switchExecutionTab;
window.cancelExecution = cancelExecution;
window.refreshExecutions = refreshExecutions;
