# 自动化测试平台

一个功能完整的自动化测试用例管理平台，支持测试用例的创建、编辑、执行和管理。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动服务器
```bash
npm start
```

服务器将在 http://localhost:3000 启动

## 📋 功能特性

### ✅ 已实现功能

#### 1. 控制面板
- 实时统计数据展示
- 项目总数、测试用例数、成功率统计
- 最近测试用例列表

#### 2. 项目管理 (完整功能)
- **新建项目** ✅
  - 完整的项目信息表单
  - 项目基本信息（名称、描述、负责人）
  - 项目时间管理（开始日期、结束日期）
  - 项目属性（优先级、状态、标签）
  - 表单验证和数据保存

- **查看项目列表** ✅
  - 卡片式项目展示
  - 项目统计信息（成员数、测试用例数）
  - 项目状态和优先级标识
  - 项目标签展示

- **筛选和搜索** ✅
  - 按项目状态筛选（进行中、已完成、已暂停、已取消）
  - 按项目负责人筛选
  - 按优先级筛选
  - 实时搜索项目名称

- **查看项目详情** ✅
  - 完整项目信息展示
  - 项目统计数据（成员数、测试用例数、通过率）
  - 项目成员列表
  - 测试用例统计分析

- **编辑项目** ✅
  - 加载现有项目数据
  - 表单预填充
  - 更新保存

- **删除项目** ✅
  - 单个删除
  - 批量删除
  - 安全检查（有测试用例的项目不能删除）
  - 确认对话框

- **项目成员管理** ✅
  - 查看项目成员列表
  - 成员角色显示
  - 成员管理功能（添加、编辑、移除）

#### 3. 测试用例管理 (完整功能)
- **新建测试用例** ✅
  - 完整的表单界面
  - 支持测试步骤动态添加/删除
  - 表单验证
  - 实时保存到数据库

- **查看测试用例列表** ✅
  - 分页显示
  - 实时数据加载
  - 状态和优先级标识

- **筛选和搜索** ✅
  - 按项目筛选
  - 按状态筛选（已通过、失败、待执行等）
  - 按优先级筛选
  - 按创建人筛选
  - 实时搜索用例名称

- **查看用例详情** ✅
  - 完整信息展示
  - 测试步骤列表
  - 执行历史

- **编辑测试用例** ✅
  - 加载现有数据
  - 表单预填充
  - 更新保存

- **删除用例** ✅
  - 单个删除
  - 批量删除
  - 确认对话框

- **执行用例** ✅
  - 环境选择
  - 执行记录
  - 状态更新

- **批量操作** ✅
  - 全选/取消全选
  - 批量执行
  - 批量删除

## 🎯 如何使用新建用例功能

### 步骤 1: 访问测试用例页面
1. 打开浏览器访问 http://localhost:3000
2. 点击左侧导航栏的"测试用例"

### 步骤 2: 创建新测试用例
1. 点击右上角的"新建用例"按钮
2. 填写表单信息：
   - **用例名称** (必填): 输入测试用例的名称
   - **所属项目** (必填): 从下拉列表选择项目
   - **优先级**: 选择高/中/低
   - **用例类型**: 功能测试/接口测试/性能测试/UI测试/集成测试
   - **执行方式**: 手动/自动
   - **用例描述**: 详细描述测试用例
   - **前置条件**: 执行前需要满足的条件
   - **测试步骤**: 点击"添加步骤"动态添加测试步骤
   - **预期结果**: 描述预期的测试结果

### 步骤 3: 管理测试步骤
- 点击"添加步骤"按钮添加新的测试步骤
- 点击步骤右侧的删除按钮移除步骤
- 步骤会自动编号

### 步骤 4: 保存用例
- 点击"保存"按钮保存测试用例
- 系统会验证必填字段
- 保存成功后会显示成功提示
- 自动返回测试用例列表

## 🔧 技术架构

### 后端
- **Node.js + Express**: Web服务器
- **SQLite**: 轻量级数据库
- **RESTful API**: 标准API设计

### 前端
- **HTML + CSS + JavaScript**: 原生前端技术
- **Tailwind CSS**: UI框架
- **Font Awesome**: 图标库

### 数据库结构
- **projects**: 项目表
  - 基本信息：id, name, description, owner
  - 时间管理：start_date, end_date, created_at, updated_at
  - 项目属性：status, priority, tags
- **project_members**: 项目成员表
  - 成员信息：id, project_id, user_name, role, joined_at
- **testcases**: 测试用例表
  - 基本信息：id, name, description, project_id
  - 用例属性：priority, type, execution_type, status
  - 测试内容：precondition, steps, expected_result
  - 管理信息：creator, created_at, updated_at, last_execution
- **test_executions**: 测试执行记录表
  - 执行信息：id, testcase_id, environment, status, result
  - 执行详情：notes, executor, execution_time

## 📊 API 接口

### 项目管理相关
- `GET /api/projects` - 获取项目列表（支持筛选和搜索）
- `GET /api/projects/:id` - 获取单个项目详情
- `POST /api/projects` - 创建新项目
- `PUT /api/projects/:id` - 更新项目
- `DELETE /api/projects/:id` - 删除项目

### 项目成员管理
- `POST /api/projects/:id/members` - 添加项目成员
- `PUT /api/projects/:id/members/:memberId` - 更新成员角色
- `DELETE /api/projects/:id/members/:memberId` - 移除项目成员

### 测试用例相关
- `GET /api/testcases` - 获取测试用例列表
- `GET /api/testcases/:id` - 获取单个测试用例
- `POST /api/testcases` - 创建新测试用例
- `PUT /api/testcases/:id` - 更新测试用例
- `DELETE /api/testcases/:id` - 删除测试用例
- `DELETE /api/testcases` - 批量删除测试用例
- `POST /api/testcases/:id/execute` - 执行测试用例

## 🎨 界面特性

- **响应式设计**: 支持桌面和移动端
- **现代化UI**: 使用Tailwind CSS
- **流畅动画**: 页面切换和加载动画
- **实时反馈**: 操作成功/失败提示
- **表单验证**: 客户端和服务端双重验证

## 🔄 开发中功能

- 项目管理模块
- 测试执行模块
- 测试报告模块
- 团队管理模块
- 系统设置模块

## 📝 示例数据

系统启动时会自动创建示例数据：
- 3个示例项目
- 3个示例测试用例
- 包含完整的测试步骤和预期结果

## 🐛 故障排除

### 服务器无法启动
1. 确保Node.js已安装
2. 运行 `npm install` 安装依赖
3. 检查端口3000是否被占用

### 数据库问题
1. 删除 `database.db` 文件
2. 重新启动服务器，系统会自动创建新数据库

### 前端功能异常
1. 检查浏览器控制台是否有JavaScript错误
2. 确保服务器正在运行
3. 刷新页面重新加载

## 📞 支持

如有问题，请检查：
1. 服务器是否正常运行
2. 浏览器控制台是否有错误信息
3. 网络连接是否正常

---

**注意**: 这是一个演示项目，用于展示测试用例管理系统的功能。在生产环境中使用前，请添加适当的安全措施和错误处理。
