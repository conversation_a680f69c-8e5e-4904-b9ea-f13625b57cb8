// 项目管理模块
let currentProjectId = null;
let selectedProjects = [];
let allProjects = [];

// 加载项目管理页面
async function loadProjectsPage() {
    const container = document.getElementById('projects-page');
    
    // 显示加载状态
    container.innerHTML = `
        <div class="flex justify-center items-center h-64">
            <div class="loading"></div>
            <span class="ml-2">加载项目...</span>
        </div>
    `;

    try {
        // 加载项目数据
        await loadProjects();
        
        // 渲染项目管理页面
        renderProjectsPage();
        
    } catch (error) {
        console.error('Error loading projects page:', error);
        container.innerHTML = `
            <div class="text-center text-red-600 p-8">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>加载项目失败</p>
                <button onclick="loadProjectsPage()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    重试
                </button>
            </div>
        `;
    }
}

async function loadProjects(filters = {}) {
    const params = new URLSearchParams(filters);
    const response = await fetch(`${API_BASE}/projects?${params}`);
    if (!response.ok) throw new Error('Failed to load projects');
    allProjects = await response.json();
}

function renderProjectsPage() {
    const container = document.getElementById('projects-page');
    
    container.innerHTML = `
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">项目管理</h2>
            <div class="flex space-x-3">
                <button id="batch-delete-projects-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-trash mr-2"></i> 批量删除
                </button>
                <button id="new-project-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i> 新建项目
                </button>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <select id="filter-status" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部状态</option>
                        <option value="active">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="paused">已暂停</option>
                        <option value="cancelled">已取消</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人</label>
                    <select id="filter-owner" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部成员</option>
                        ${getUniqueOwners().map(owner => `<option value="${owner}">${owner}</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                    <select id="filter-priority" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部优先级</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索项目</label>
                    <input id="search-project" type="text" placeholder="输入项目名称..." class="w-full border rounded-lg px-3 py-2">
                </div>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            ${renderProjectCards()}
        </div>

        ${allProjects.length === 0 ? `
            <div class="text-center py-12">
                <i class="fas fa-folder-open text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
                <p class="text-gray-500 mb-4">开始创建您的第一个测试项目</p>
                <button onclick="openNewProjectModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-plus mr-2"></i> 新建项目
                </button>
            </div>
        ` : ''}
    `;

    // 绑定事件
    bindProjectEvents();
}

function renderProjectCards() {
    return allProjects.map(project => `
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow project-card" data-project-id="${project.id}">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" class="project-checkbox mr-3 rounded" value="${project.id}">
                        <h3 class="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600" onclick="viewProjectDetail('${project.id}')">
                            ${project.name}
                        </h3>
                    </div>
                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getProjectStatusClass(project.status)}">
                        ${getProjectStatusText(project.status)}
                    </span>
                </div>
                
                <p class="text-gray-600 text-sm mb-4 line-clamp-2">${project.description || '无描述'}</p>
                
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-user mr-1"></i> ${project.owner || '未指定'}</span>
                        <span><i class="fas fa-users mr-1"></i> ${project.member_count || 0}</span>
                        <span><i class="fas fa-tasks mr-1"></i> ${project.testcase_count || 0}</span>
                    </div>
                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getPriorityClass(project.priority)}">
                        ${getPriorityText(project.priority)}
                    </span>
                </div>

                ${project.tags && project.tags.length > 0 ? `
                    <div class="flex flex-wrap gap-1 mb-4">
                        ${project.tags.slice(0, 3).map(tag => `
                            <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">${tag}</span>
                        `).join('')}
                        ${project.tags.length > 3 ? `<span class="text-xs text-gray-500">+${project.tags.length - 3}</span>` : ''}
                    </div>
                ` : ''}

                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">
                        创建于 ${formatDate(project.created_at)}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="viewProjectDetail('${project.id}')" class="text-blue-600 hover:text-blue-900" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editProject('${project.id}')" class="text-green-600 hover:text-green-900" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteProject('${project.id}')" class="text-red-600 hover:text-red-900" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function bindProjectEvents() {
    // 筛选功能
    const filterElements = ['filter-status', 'filter-owner', 'filter-priority'];
    filterElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', applyProjectFilters);
        }
    });

    // 搜索功能
    const searchInput = document.getElementById('search-project');
    if (searchInput) {
        searchInput.addEventListener('input', applyProjectFilters);
    }

    // 按钮事件
    const newProjectBtn = document.getElementById('new-project-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-projects-btn');

    if (newProjectBtn) {
        newProjectBtn.addEventListener('click', openNewProjectModal);
    }
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', batchDeleteProjects);
    }

    // 复选框事件
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('project-checkbox')) {
            updateSelectedProjects();
        }
    });
}

function updateSelectedProjects() {
    const checkboxes = document.querySelectorAll('.project-checkbox:checked');
    selectedProjects = Array.from(checkboxes).map(cb => cb.value);
    
    const batchDeleteBtn = document.getElementById('batch-delete-projects-btn');
    if (batchDeleteBtn) {
        batchDeleteBtn.disabled = selectedProjects.length === 0;
    }
}

async function applyProjectFilters() {
    const filters = {
        status: document.getElementById('filter-status').value,
        owner: document.getElementById('filter-owner').value,
        priority: document.getElementById('filter-priority').value,
        search: document.getElementById('search-project').value
    };

    // 移除空值
    Object.keys(filters).forEach(key => {
        if (!filters[key]) delete filters[key];
    });

    try {
        await loadProjects(filters);
        
        // 更新项目卡片
        const container = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (container) {
            container.innerHTML = renderProjectCards();
        }

        // 重置选择状态
        selectedProjects = [];
        updateSelectedProjects();

    } catch (error) {
        console.error('Error applying filters:', error);
        showNotification('筛选失败', 'error');
    }
}

function getUniqueOwners() {
    const owners = allProjects.map(p => p.owner).filter(Boolean);
    return [...new Set(owners)];
}

function getProjectStatusClass(status) {
    switch(status) {
        case 'active': return 'bg-green-100 text-green-800';
        case 'completed': return 'bg-blue-100 text-blue-800';
        case 'paused': return 'bg-yellow-100 text-yellow-800';
        case 'cancelled': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getProjectStatusText(status) {
    switch(status) {
        case 'active': return '进行中';
        case 'completed': return '已完成';
        case 'paused': return '已暂停';
        case 'cancelled': return '已取消';
        default: return '未知';
    }
}

// 项目操作函数
function openNewProjectModal() {
    currentProjectId = null;
    showProjectModal('新建项目', {});
}

async function editProject(id) {
    try {
        const response = await fetch(`${API_BASE}/projects/${id}`);
        if (!response.ok) throw new Error('Failed to load project');
        
        const project = await response.json();
        currentProjectId = id;
        showProjectModal('编辑项目', project);
        
    } catch (error) {
        console.error('Error loading project for edit:', error);
        showNotification('加载项目失败', 'error');
    }
}

async function viewProjectDetail(id) {
    try {
        const response = await fetch(`${API_BASE}/projects/${id}`);
        if (!response.ok) throw new Error('Failed to load project');
        
        const project = await response.json();
        showProjectDetailModal(project);
        
    } catch (error) {
        console.error('Error loading project detail:', error);
        showNotification('加载项目详情失败', 'error');
    }
}

async function deleteProject(id) {
    if (!confirm('确定要删除这个项目吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/projects/${id}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to delete project');
        }

        showNotification('项目删除成功', 'success');
        
        // 重新加载项目列表
        await loadProjects();
        const container = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (container) {
            container.innerHTML = renderProjectCards();
        }

    } catch (error) {
        console.error('Error deleting project:', error);
        if (error.message.includes('existing test cases')) {
            showNotification('无法删除包含测试用例的项目，请先删除相关测试用例', 'error');
        } else {
            showNotification('删除项目失败', 'error');
        }
    }
}

async function batchDeleteProjects() {
    if (selectedProjects.length === 0) return;
    
    if (!confirm(`确定要删除选中的 ${selectedProjects.length} 个项目吗？此操作不可撤销。`)) {
        return;
    }

    let successCount = 0;
    let errorCount = 0;

    for (const projectId of selectedProjects) {
        try {
            const response = await fetch(`${API_BASE}/projects/${projectId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                successCount++;
            } else {
                errorCount++;
            }
        } catch (error) {
            errorCount++;
        }
    }

    if (successCount > 0) {
        showNotification(`成功删除 ${successCount} 个项目`, 'success');
    }
    if (errorCount > 0) {
        showNotification(`${errorCount} 个项目删除失败`, 'error');
    }

    // 重新加载项目列表
    await loadProjects();
    const container = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
    if (container) {
        container.innerHTML = renderProjectCards();
    }

    // 重置选择状态
    selectedProjects = [];
    updateSelectedProjects();
}

// 项目表单模态框
function showProjectModal(title, project = {}) {
    const modalHtml = `
        <div id="project-form-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        <button onclick="closeProjectFormModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="project-form" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 *</label>
                                <input id="project-name" type="text" required value="${project.name || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入项目名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人</label>
                                <input id="project-owner" type="text" value="${project.owner || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入负责人姓名">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
                            <textarea id="project-description" rows="3"
                                      class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入项目的详细描述">${project.description || ''}</textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                <select id="project-priority" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="high" ${project.priority === 'high' ? 'selected' : ''}>高</option>
                                    <option value="medium" ${project.priority === 'medium' || !project.priority ? 'selected' : ''}>中</option>
                                    <option value="low" ${project.priority === 'low' ? 'selected' : ''}>低</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                                <select id="project-status" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="active" ${project.status === 'active' || !project.status ? 'selected' : ''}>进行中</option>
                                    <option value="paused" ${project.status === 'paused' ? 'selected' : ''}>已暂停</option>
                                    <option value="completed" ${project.status === 'completed' ? 'selected' : ''}>已完成</option>
                                    <option value="cancelled" ${project.status === 'cancelled' ? 'selected' : ''}>已取消</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目标签</label>
                                <input id="project-tags" type="text" value="${project.tags ? project.tags.join(', ') : ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="用逗号分隔多个标签">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                                <input id="project-start-date" type="date" value="${project.start_date || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                                <input id="project-end-date" type="date" value="${project.end_date || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeProjectFormModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('project-form');
    form.addEventListener('submit', handleProjectFormSubmit);
}

function closeProjectFormModal() {
    const modal = document.getElementById('project-form-modal');
    if (modal) {
        modal.remove();
    }
    currentProjectId = null;
}

async function handleProjectFormSubmit(e) {
    e.preventDefault();

    // 收集表单数据
    const formData = {
        name: document.getElementById('project-name').value.trim(),
        description: document.getElementById('project-description').value.trim(),
        owner: document.getElementById('project-owner').value.trim(),
        priority: document.getElementById('project-priority').value,
        status: document.getElementById('project-status').value,
        start_date: document.getElementById('project-start-date').value,
        end_date: document.getElementById('project-end-date').value,
        tags: document.getElementById('project-tags').value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0)
    };

    // 验证必填字段
    if (!formData.name) {
        showNotification('请输入项目名称', 'error');
        return;
    }

    // 验证日期
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
        showNotification('开始日期不能晚于结束日期', 'error');
        return;
    }

    try {
        let response;
        if (currentProjectId) {
            // 更新项目
            response = await fetch(`${API_BASE}/projects/${currentProjectId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // 创建新项目
            response = await fetch(`${API_BASE}/projects`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        if (!response.ok) {
            throw new Error('Failed to save project');
        }

        showNotification(
            currentProjectId ? '项目更新成功' : '项目创建成功',
            'success'
        );

        closeProjectFormModal();

        // 重新加载项目列表
        await loadProjects();
        const container = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
        if (container) {
            container.innerHTML = renderProjectCards();
        }

    } catch (error) {
        console.error('Error saving project:', error);
        showNotification('保存项目失败', 'error');
    }
}

// 项目详情模态框
function showProjectDetailModal(project) {
    const modalHtml = `
        <div id="project-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-medium text-gray-900">项目详情</h3>
                        <button onclick="closeProjectDetailModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- 项目基本信息 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">${project.name}</h4>
                                <p class="text-gray-600">${project.description || '无描述'}</p>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">负责人:</span>
                                    <span class="text-sm font-medium">${project.owner || '未指定'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">状态:</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getProjectStatusClass(project.status)}">
                                        ${getProjectStatusText(project.status)}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">优先级:</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getPriorityClass(project.priority)}">
                                        ${getPriorityText(project.priority)}
                                    </span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">开始日期:</span>
                                    <span class="text-sm font-medium">${project.start_date || '未设置'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">结束日期:</span>
                                    <span class="text-sm font-medium">${project.end_date || '未设置'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">创建时间:</span>
                                    <span class="text-sm font-medium">${formatDate(project.created_at)}</span>
                                </div>
                            </div>
                        </div>

                        ${project.tags && project.tags.length > 0 ? `
                            <div class="mt-4">
                                <span class="text-sm text-gray-500 mr-2">标签:</span>
                                ${project.tags.map(tag => `
                                    <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded mr-1">${tag}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>

                    <!-- 统计信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">${project.member_count || 0}</div>
                            <div class="text-sm text-gray-500">团队成员</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">${project.testcase_count || 0}</div>
                            <div class="text-sm text-gray-500">测试用例</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600">${calculatePassRate(project.testcase_stats)}%</div>
                            <div class="text-sm text-gray-500">通过率</div>
                        </div>
                    </div>

                    <!-- 项目成员和测试用例统计 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 项目成员 -->
                        <div class="bg-white border rounded-lg">
                            <div class="px-4 py-3 border-b flex justify-between items-center">
                                <h4 class="font-medium text-gray-900">项目成员 (${project.members ? project.members.length : 0})</h4>
                                <button onclick="addProjectMember('${project.id}')" class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-plus mr-1"></i> 添加成员
                                </button>
                            </div>
                            <div class="p-4">
                                ${project.members && project.members.length > 0 ? `
                                    <div class="space-y-3">
                                        ${project.members.map(member => `
                                            <div class="flex justify-between items-center">
                                                <div class="flex items-center">
                                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-sm">${member.user_name}</div>
                                                        <div class="text-xs text-gray-500">${getRoleText(member.role)}</div>
                                                    </div>
                                                </div>
                                                <div class="flex space-x-2">
                                                    <button onclick="editMemberRole('${member.id}')" class="text-blue-600 hover:text-blue-800 text-xs">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button onclick="removeMember('${project.id}', '${member.id}')" class="text-red-600 hover:text-red-800 text-xs">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : `
                                    <div class="text-center text-gray-500 py-4">
                                        <i class="fas fa-users text-2xl mb-2"></i>
                                        <p>暂无成员</p>
                                    </div>
                                `}
                            </div>
                        </div>

                        <!-- 测试用例统计 -->
                        <div class="bg-white border rounded-lg">
                            <div class="px-4 py-3 border-b">
                                <h4 class="font-medium text-gray-900">测试用例统计</h4>
                            </div>
                            <div class="p-4">
                                ${project.testcase_stats && project.testcase_stats.length > 0 ? `
                                    <div class="space-y-3">
                                        ${project.testcase_stats.map(stat => `
                                            <div class="flex justify-between items-center">
                                                <span class="text-sm text-gray-600">${getStatusText(stat.status)}</span>
                                                <span class="font-medium">${stat.count}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : `
                                    <div class="text-center text-gray-500 py-4">
                                        <i class="fas fa-tasks text-2xl mb-2"></i>
                                        <p>暂无测试用例</p>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t mt-6">
                        <button onclick="closeProjectDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            关闭
                        </button>
                        <button onclick="editProject('${project.id}')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i class="fas fa-edit mr-1"></i> 编辑项目
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeProjectDetailModal() {
    const modal = document.getElementById('project-detail-modal');
    if (modal) {
        modal.remove();
    }
}

function calculatePassRate(testcaseStats) {
    if (!testcaseStats || testcaseStats.length === 0) return 0;

    const total = testcaseStats.reduce((sum, stat) => sum + stat.count, 0);
    const passed = testcaseStats.find(stat => stat.status === 'passed');

    if (total === 0) return 0;
    return Math.round(((passed ? passed.count : 0) / total) * 100);
}

function getRoleText(role) {
    switch(role) {
        case 'owner': return '项目负责人';
        case 'tester': return '测试工程师';
        case 'developer': return '开发工程师';
        case 'member': return '项目成员';
        default: return '成员';
    }
}

// 成员管理功能（简化版）
function addProjectMember(projectId) {
    showNotification('添加成员功能开发中...', 'info');
}

function editMemberRole(memberId) {
    showNotification('编辑成员角色功能开发中...', 'info');
}

function removeMember(projectId, memberId) {
    showNotification('移除成员功能开发中...', 'info');
}

// 全局函数
window.loadProjectsPage = loadProjectsPage;
window.viewProjectDetail = viewProjectDetail;
window.editProject = editProject;
window.deleteProject = deleteProject;
window.openNewProjectModal = openNewProjectModal;
window.closeProjectFormModal = closeProjectFormModal;
window.closeProjectDetailModal = closeProjectDetailModal;
window.addProjectMember = addProjectMember;
window.editMemberRole = editMemberRole;
window.removeMember = removeMember;
