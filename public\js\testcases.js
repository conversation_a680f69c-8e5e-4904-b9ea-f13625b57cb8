// 测试用例模块
let currentTestcaseId = null;
let selectedTestcases = [];
let allTestcases = [];
let allProjects = [];

// 加载测试用例页面
async function loadTestcasesPage() {
    const container = document.getElementById('testcases-page');
    
    // 显示加载状态
    container.innerHTML = `
        <div class="flex justify-center items-center h-64">
            <div class="loading"></div>
            <span class="ml-2">加载测试用例...</span>
        </div>
    `;

    try {
        // 加载项目和测试用例数据
        await Promise.all([
            loadProjects(),
            loadTestcases()
        ]);

        // 渲染测试用例页面
        renderTestcasesPage();
        
    } catch (error) {
        console.error('Error loading testcases page:', error);
        container.innerHTML = `
            <div class="text-center text-red-600 p-8">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>加载测试用例失败</p>
                <button onclick="loadTestcasesPage()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    重试
                </button>
            </div>
        `;
    }
}

async function loadProjects() {
    const response = await fetch(`${API_BASE}/projects`);
    if (!response.ok) throw new Error('Failed to load projects');
    allProjects = await response.json();
}

async function loadTestcases(filters = {}) {
    const params = new URLSearchParams(filters);
    const response = await fetch(`${API_BASE}/testcases?${params}`);
    if (!response.ok) throw new Error('Failed to load testcases');
    allTestcases = await response.json();
}

function renderTestcasesPage() {
    const container = document.getElementById('testcases-page');
    
    container.innerHTML = `
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">测试用例管理</h2>
            <div class="flex space-x-3">
                <button id="batch-execute-btn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-play mr-2"></i> 批量执行
                </button>
                <button id="batch-delete-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-trash mr-2"></i> 批量删除
                </button>
                <button id="new-testcase-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i> 新建用例
                </button>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属项目</label>
                    <select id="filter-project" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部项目</option>
                        ${allProjects.map(project => `<option value="${project.id}">${project.name}</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">用例状态</label>
                    <select id="filter-status" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部状态</option>
                        <option value="passed">已通过</option>
                        <option value="failed">失败</option>
                        <option value="pending">待执行</option>
                        <option value="draft">草稿</option>
                        <option value="skipped">跳过</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                    <select id="filter-priority" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部优先级</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">创建人</label>
                    <select id="filter-creator" class="w-full border rounded-lg px-3 py-2">
                        <option value="">全部成员</option>
                        ${getUniqueCreators().map(creator => `<option value="${creator}">${creator}</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索用例</label>
                    <input id="search-testcase" type="text" placeholder="输入用例名称..." class="w-full border rounded-lg px-3 py-2">
                </div>
            </div>
        </div>

        <!-- 测试用例列表 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b bg-gray-50">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">测试用例列表 (${allTestcases.length})</h3>
                    <div class="flex space-x-2">
                        <button onclick="exportTestcases()" class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded">
                            <i class="fas fa-download mr-1"></i> 导出
                        </button>
                        <button onclick="importTestcases()" class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded">
                            <i class="fas fa-upload mr-1"></i> 导入
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input id="select-all-checkbox" type="checkbox" class="rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用例名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后执行</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="testcase-table-body" class="bg-white divide-y divide-gray-200">
                        ${renderTestcaseRows()}
                    </tbody>
                </table>
            </div>
            ${allTestcases.length === 0 ? `
                <div class="p-8 text-center text-gray-500">
                    <i class="fas fa-inbox text-4xl mb-4"></i>
                    <p>暂无测试用例</p>
                    <button onclick="openNewTestcaseModal()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        创建第一个测试用例
                    </button>
                </div>
            ` : ''}
        </div>
    `;

    // 绑定事件
    bindTestcaseEvents();
}

function renderTestcaseRows() {
    return allTestcases.map(testcase => `
        <tr class="hover:bg-gray-50 testcase-row" data-testcase-id="${testcase.id}">
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="rounded testcase-checkbox" value="${testcase.id}">
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600" onclick="viewTestcaseDetail('${testcase.id}')">
                    ${testcase.name}
                </div>
                <div class="text-sm text-gray-500">${testcase.description || '无描述'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${testcase.project_name || '未知项目'}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityClass(testcase.priority)}">
                    ${getPriorityText(testcase.priority)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(testcase.status)}">
                    ${getStatusText(testcase.status)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${testcase.creator || '未知'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(testcase.last_execution)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="viewTestcaseDetail('${testcase.id}')" class="text-blue-600 hover:text-blue-900" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="editTestcase('${testcase.id}')" class="text-green-600 hover:text-green-900" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="executeTestcase('${testcase.id}')" class="text-purple-600 hover:text-purple-900" title="执行">
                        <i class="fas fa-play"></i>
                    </button>
                    <button onclick="deleteTestcase('${testcase.id}')" class="text-red-600 hover:text-red-900" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function bindTestcaseEvents() {
    // 全选功能
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.testcase-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedTestcases();
        });
    }

    // 单个复选框变化
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('testcase-checkbox')) {
            updateSelectedTestcases();
        }
    });

    // 筛选功能
    const filterElements = ['filter-project', 'filter-status', 'filter-priority', 'filter-creator'];
    filterElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', applyFilters);
        }
    });

    // 搜索功能
    const searchInput = document.getElementById('search-testcase');
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }

    // 按钮事件
    const newTestcaseBtn = document.getElementById('new-testcase-btn');
    const batchExecuteBtn = document.getElementById('batch-execute-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');

    if (newTestcaseBtn) {
        newTestcaseBtn.addEventListener('click', openNewTestcaseModal);
    }
    if (batchExecuteBtn) {
        batchExecuteBtn.addEventListener('click', batchExecuteTestcases);
    }
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', batchDeleteTestcases);
    }
}

function updateSelectedTestcases() {
    const checkboxes = document.querySelectorAll('.testcase-checkbox:checked');
    selectedTestcases = Array.from(checkboxes).map(cb => cb.value);
    
    const batchExecuteBtn = document.getElementById('batch-execute-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    
    if (batchExecuteBtn && batchDeleteBtn) {
        const hasSelected = selectedTestcases.length > 0;
        batchExecuteBtn.disabled = !hasSelected;
        batchDeleteBtn.disabled = !hasSelected;
    }

    // 更新全选状态
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const allCheckboxes = document.querySelectorAll('.testcase-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = selectedTestcases.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedTestcases.length > 0 && selectedTestcases.length < allCheckboxes.length;
    }
}

async function applyFilters() {
    const filters = {
        project_id: document.getElementById('filter-project').value,
        status: document.getElementById('filter-status').value,
        priority: document.getElementById('filter-priority').value,
        creator: document.getElementById('filter-creator').value,
        search: document.getElementById('search-testcase').value
    };

    // 移除空值
    Object.keys(filters).forEach(key => {
        if (!filters[key]) delete filters[key];
    });

    try {
        await loadTestcases(filters);
        
        // 更新表格内容
        const tbody = document.getElementById('testcase-table-body');
        if (tbody) {
            tbody.innerHTML = renderTestcaseRows();
        }

        // 更新计数
        const countElement = document.querySelector('.text-lg.font-semibold');
        if (countElement) {
            countElement.textContent = `测试用例列表 (${allTestcases.length})`;
        }

        // 重置选择状态
        selectedTestcases = [];
        updateSelectedTestcases();

    } catch (error) {
        console.error('Error applying filters:', error);
        showNotification('筛选失败', 'error');
    }
}

function getUniqueCreators() {
    const creators = allTestcases.map(tc => tc.creator).filter(Boolean);
    return [...new Set(creators)];
}

// 测试用例操作函数
function openNewTestcaseModal() {
    currentTestcaseId = null;
    showTestcaseModal('新建测试用例', {});
}

async function editTestcase(id) {
    try {
        const response = await fetch(`${API_BASE}/testcases/${id}`);
        if (!response.ok) throw new Error('Failed to load testcase');

        const testcase = await response.json();
        currentTestcaseId = id;
        showTestcaseModal('编辑测试用例', testcase);

    } catch (error) {
        console.error('Error loading testcase for edit:', error);
        showNotification('加载测试用例失败', 'error');
    }
}

async function viewTestcaseDetail(id) {
    try {
        const response = await fetch(`${API_BASE}/testcases/${id}`);
        if (!response.ok) throw new Error('Failed to load testcase');
        
        const testcase = await response.json();
        showTestcaseDetailModal(testcase);
        
    } catch (error) {
        console.error('Error loading testcase detail:', error);
        showNotification('加载测试用例详情失败', 'error');
    }
}

function showTestcaseDetailModal(testcase) {
    // 创建模态框HTML
    const modalHtml = `
        <div id="testcase-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">测试用例详情</h3>
                        <button onclick="closeTestcaseDetailModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">用例名称</label>
                                <p class="mt-1 text-sm text-gray-900">${testcase.name}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">所属项目</label>
                                <p class="mt-1 text-sm text-gray-900">${testcase.project_name || '未知'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">优先级</label>
                                <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityClass(testcase.priority)}">
                                    ${getPriorityText(testcase.priority)}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">状态</label>
                                <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(testcase.status)}">
                                    ${getStatusText(testcase.status)}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">用例类型</label>
                                <p class="mt-1 text-sm text-gray-900">${testcase.type || '未知'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">执行方式</label>
                                <p class="mt-1 text-sm text-gray-900">${testcase.execution_type === 'automated' ? '自动' : '手动'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">创建人</label>
                                <p class="mt-1 text-sm text-gray-900">${testcase.creator || '未知'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">最后执行</label>
                                <p class="mt-1 text-sm text-gray-900">${formatDate(testcase.last_execution)}</p>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">用例描述</label>
                            <p class="mt-1 text-sm text-gray-900">${testcase.description || '无描述'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">前置条件</label>
                            <p class="mt-1 text-sm text-gray-900">${testcase.precondition || '无前置条件'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">测试步骤</label>
                            <ol class="mt-1 text-sm text-gray-900 list-decimal list-inside space-y-1">
                                ${testcase.steps && testcase.steps.length > 0 
                                    ? testcase.steps.map(step => `<li>${step}</li>`).join('') 
                                    : '<li>无测试步骤</li>'}
                            </ol>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">预期结果</label>
                            <p class="mt-1 text-sm text-gray-900">${testcase.expected_result || '无预期结果'}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button onclick="closeTestcaseDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            关闭
                        </button>
                        <button onclick="editTestcase('${testcase.id}')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            编辑
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeTestcaseDetailModal() {
    const modal = document.getElementById('testcase-detail-modal');
    if (modal) {
        modal.remove();
    }
}

async function deleteTestcase(id) {
    if (!confirm('确定要删除这个测试用例吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/testcases/${id}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Failed to delete testcase');

        showNotification('测试用例删除成功', 'success');
        
        // 重新加载测试用例列表
        await loadTestcases();
        const tbody = document.getElementById('testcase-table-body');
        if (tbody) {
            tbody.innerHTML = renderTestcaseRows();
        }

    } catch (error) {
        console.error('Error deleting testcase:', error);
        showNotification('删除测试用例失败', 'error');
    }
}

async function executeTestcase(id) {
    const environment = prompt('请选择执行环境:\n1. test (测试环境)\n2. staging (预发布环境)\n3. production (生产环境)', 'test');
    
    if (!environment) return;

    try {
        const response = await fetch(`${API_BASE}/testcases/${id}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                environment,
                executor: '张测试',
                notes: ''
            })
        });

        if (!response.ok) throw new Error('Failed to execute testcase');

        const result = await response.json();
        showNotification(`测试用例开始在${environment}环境执行`, 'success');

    } catch (error) {
        console.error('Error executing testcase:', error);
        showNotification('执行测试用例失败', 'error');
    }
}

function batchExecuteTestcases() {
    if (selectedTestcases.length === 0) return;
    showNotification(`开始批量执行 ${selectedTestcases.length} 个测试用例`, 'info');
}

async function batchDeleteTestcases() {
    if (selectedTestcases.length === 0) return;
    
    if (!confirm(`确定要删除选中的 ${selectedTestcases.length} 个测试用例吗？此操作不可撤销。`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/testcases`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ids: selectedTestcases })
        });

        if (!response.ok) throw new Error('Failed to delete testcases');

        const result = await response.json();
        showNotification(`成功删除 ${result.deletedCount} 个测试用例`, 'success');
        
        // 重新加载测试用例列表
        await loadTestcases();
        const tbody = document.getElementById('testcase-table-body');
        if (tbody) {
            tbody.innerHTML = renderTestcaseRows();
        }

        // 重置选择状态
        selectedTestcases = [];
        updateSelectedTestcases();

    } catch (error) {
        console.error('Error batch deleting testcases:', error);
        showNotification('批量删除失败', 'error');
    }
}

function exportTestcases() {
    showNotification('导出功能开发中...', 'info');
}

function importTestcases() {
    showNotification('导入功能开发中...', 'info');
}

// 测试用例表单模态框
function showTestcaseModal(title, testcase = {}) {
    const modalHtml = `
        <div id="testcase-form-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        <button onclick="closeTestcaseFormModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="testcase-form" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用例名称 *</label>
                                <input id="testcase-name" type="text" required value="${testcase.name || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入测试用例名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属项目 *</label>
                                <select id="testcase-project" required class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择项目</option>
                                    ${allProjects.map(project =>
                                        `<option value="${project.id}" ${testcase.project_id === project.id ? 'selected' : ''}>${project.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                <select id="testcase-priority" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="high" ${testcase.priority === 'high' ? 'selected' : ''}>高</option>
                                    <option value="medium" ${testcase.priority === 'medium' || !testcase.priority ? 'selected' : ''}>中</option>
                                    <option value="low" ${testcase.priority === 'low' ? 'selected' : ''}>低</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用例类型</label>
                                <select id="testcase-type" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="functional" ${testcase.type === 'functional' || !testcase.type ? 'selected' : ''}>功能测试</option>
                                    <option value="api" ${testcase.type === 'api' ? 'selected' : ''}>接口测试</option>
                                    <option value="performance" ${testcase.type === 'performance' ? 'selected' : ''}>性能测试</option>
                                    <option value="ui" ${testcase.type === 'ui' ? 'selected' : ''}>UI测试</option>
                                    <option value="integration" ${testcase.type === 'integration' ? 'selected' : ''}>集成测试</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行方式</label>
                                <select id="testcase-execution" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="manual" ${testcase.execution_type === 'manual' || !testcase.execution_type ? 'selected' : ''}>手动</option>
                                    <option value="automated" ${testcase.execution_type === 'automated' ? 'selected' : ''}>自动</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">用例描述</label>
                            <textarea id="testcase-description" rows="3"
                                      class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入测试用例的详细描述">${testcase.description || ''}</textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">前置条件</label>
                            <textarea id="testcase-precondition" rows="2"
                                      class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="执行此用例前需要满足的条件">${testcase.precondition || ''}</textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">测试步骤</label>
                            <div id="test-steps-container">
                                ${renderTestSteps(testcase.steps || [])}
                            </div>
                            <button type="button" onclick="addTestStep()" class="text-blue-600 hover:text-blue-800 text-sm mt-2">
                                <i class="fas fa-plus-circle mr-1"></i> 添加步骤
                            </button>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">预期结果</label>
                            <textarea id="testcase-expected" rows="3"
                                      class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="描述执行测试步骤后的预期结果">${testcase.expected_result || ''}</textarea>
                        </div>
                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeTestcaseFormModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('testcase-form');
    form.addEventListener('submit', handleTestcaseFormSubmit);

    // 如果没有测试步骤，添加一个空步骤
    if (!testcase.steps || testcase.steps.length === 0) {
        addTestStep();
    }
}

function renderTestSteps(steps) {
    if (!steps || steps.length === 0) {
        return '';
    }

    return steps.map((step, index) => `
        <div class="test-step flex items-center space-x-2 mb-2">
            <span class="text-sm text-gray-500 w-8">${index + 1}.</span>
            <input type="text" value="${step}" class="flex-1 border rounded px-3 py-2 text-sm" placeholder="输入测试步骤">
            <button type="button" onclick="removeTestStep(this)" class="text-red-600 hover:text-red-800">
                <i class="fas fa-minus-circle"></i>
            </button>
        </div>
    `).join('');
}

function addTestStep() {
    const container = document.getElementById('test-steps-container');
    const stepCount = container.children.length + 1;

    const stepDiv = document.createElement('div');
    stepDiv.className = 'test-step flex items-center space-x-2 mb-2';
    stepDiv.innerHTML = `
        <span class="text-sm text-gray-500 w-8">${stepCount}.</span>
        <input type="text" class="flex-1 border rounded px-3 py-2 text-sm" placeholder="输入测试步骤">
        <button type="button" onclick="removeTestStep(this)" class="text-red-600 hover:text-red-800">
            <i class="fas fa-minus-circle"></i>
        </button>
    `;

    container.appendChild(stepDiv);
}

function removeTestStep(button) {
    const container = document.getElementById('test-steps-container');
    if (container.children.length > 1) {
        button.closest('.test-step').remove();
        updateStepNumbers();
    }
}

function updateStepNumbers() {
    const steps = document.querySelectorAll('.test-step');
    steps.forEach((step, index) => {
        step.querySelector('span').textContent = `${index + 1}.`;
    });
}

function closeTestcaseFormModal() {
    const modal = document.getElementById('testcase-form-modal');
    if (modal) {
        modal.remove();
    }
    currentTestcaseId = null;
}

async function handleTestcaseFormSubmit(e) {
    e.preventDefault();

    // 收集表单数据
    const formData = {
        name: document.getElementById('testcase-name').value.trim(),
        description: document.getElementById('testcase-description').value.trim(),
        project_id: document.getElementById('testcase-project').value,
        priority: document.getElementById('testcase-priority').value,
        type: document.getElementById('testcase-type').value,
        execution_type: document.getElementById('testcase-execution').value,
        precondition: document.getElementById('testcase-precondition').value.trim(),
        expected_result: document.getElementById('testcase-expected').value.trim(),
        steps: getTestSteps(),
        creator: '张测试' // 这里应该从当前登录用户获取
    };

    // 验证必填字段
    if (!formData.name) {
        showNotification('请输入用例名称', 'error');
        return;
    }
    if (!formData.project_id) {
        showNotification('请选择所属项目', 'error');
        return;
    }

    try {
        let response;
        if (currentTestcaseId) {
            // 更新测试用例
            response = await fetch(`${API_BASE}/testcases/${currentTestcaseId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // 创建新测试用例
            response = await fetch(`${API_BASE}/testcases`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        if (!response.ok) {
            throw new Error('Failed to save testcase');
        }

        const result = await response.json();

        showNotification(
            currentTestcaseId ? '测试用例更新成功' : '测试用例创建成功',
            'success'
        );

        closeTestcaseFormModal();

        // 重新加载测试用例列表
        await loadTestcases();
        const tbody = document.getElementById('testcase-table-body');
        if (tbody) {
            tbody.innerHTML = renderTestcaseRows();
        }

        // 更新计数
        const countElement = document.querySelector('.text-lg.font-semibold');
        if (countElement) {
            countElement.textContent = `测试用例列表 (${allTestcases.length})`;
        }

    } catch (error) {
        console.error('Error saving testcase:', error);
        showNotification('保存测试用例失败', 'error');
    }
}

function getTestSteps() {
    const stepInputs = document.querySelectorAll('.test-step input');
    return Array.from(stepInputs)
        .map(input => input.value.trim())
        .filter(step => step.length > 0);
}

// 全局函数
window.loadTestcasesPage = loadTestcasesPage;
window.viewTestcaseDetail = viewTestcaseDetail;
window.editTestcase = editTestcase;
window.deleteTestcase = deleteTestcase;
window.executeTestcase = executeTestcase;
window.closeTestcaseDetailModal = closeTestcaseDetailModal;
window.closeTestcaseFormModal = closeTestcaseFormModal;
window.addTestStep = addTestStep;
window.removeTestStep = removeTestStep;
