// 全局变量
let currentPage = 'dashboard';
let projects = [];
let testcases = [];

// API 基础URL
const API_BASE = '/api';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 初始化导航
    initNavigation();
    
    // 初始化移动端菜单
    initMobileMenu();
    
    // 加载初始数据
    loadDashboardData();
    
    // 默认显示控制面板
    showPage('dashboard');
}

// 导航功能
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const pageId = this.getAttribute('data-page');
            showPage(pageId);
        });
    });
}

function showPage(pageId) {
    // 隐藏所有页面
    const pageContents = document.querySelectorAll('.page-content');
    pageContents.forEach(page => {
        page.classList.add('hidden');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + '-page');
    if (targetPage) {
        targetPage.classList.remove('hidden');
    }

    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('text-white', 'bg-blue-900');
        link.classList.add('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
    });

    // 激活当前导航
    const activeLink = document.querySelector(`[data-page="${pageId}"]`);
    if (activeLink) {
        activeLink.classList.remove('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
        activeLink.classList.add('text-white', 'bg-blue-900');
    }

    currentPage = pageId;

    // 根据页面加载相应数据
    switch(pageId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'testcases':
            loadTestcasesPage();
            break;
        case 'projects':
            loadProjectsPage();
            break;
    }
}

// 移动端菜单
function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('button.md\\:hidden');
    const sidebar = document.querySelector('.sidebar');
    
    if(mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('hidden');
        });
    }
}

// 加载控制面板数据
async function loadDashboardData() {
    try {
        // 加载项目数据
        const projectsResponse = await fetch(`${API_BASE}/projects`);
        projects = await projectsResponse.json();
        
        // 加载测试用例数据
        const testcasesResponse = await fetch(`${API_BASE}/testcases`);
        testcases = await testcasesResponse.json();
        
        // 更新统计数据
        updateDashboardStats();
        
        // 显示最近测试用例
        displayRecentTestcases();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showNotification('加载数据失败', 'error');
    }
}

function updateDashboardStats() {
    // 更新项目总数
    document.getElementById('total-projects').textContent = projects.length;
    
    // 更新测试用例总数
    document.getElementById('total-testcases').textContent = testcases.length;
    
    // 计算成功率
    const passedTests = testcases.filter(tc => tc.status === 'passed').length;
    const totalTests = testcases.length;
    const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
    document.getElementById('success-rate').textContent = successRate + '%';
}

function displayRecentTestcases() {
    const container = document.getElementById('recent-testcases');
    
    if (testcases.length === 0) {
        container.innerHTML = `
            <div class="p-6 text-center text-gray-500">
                <i class="fas fa-inbox text-4xl mb-2"></i>
                <p>暂无测试用例</p>
            </div>
        `;
        return;
    }

    // 显示最近的5个测试用例
    const recentTestcases = testcases.slice(0, 5);
    
    container.innerHTML = recentTestcases.map(testcase => `
        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="viewTestcaseDetail('${testcase.id}')">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">${testcase.name}</h4>
                    <p class="text-sm text-gray-500 mt-1">${testcase.description || '无描述'}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="text-xs text-gray-500">项目: ${testcase.project_name || '未知'}</span>
                        <span class="text-xs text-gray-500">创建人: ${testcase.creator || '未知'}</span>
                    </div>
                </div>
                <div class="ml-4 flex flex-col items-end">
                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(testcase.status)}">
                        ${getStatusText(testcase.status)}
                    </span>
                    <span class="text-xs text-gray-500 mt-1">${formatDate(testcase.created_at)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// 加载项目数据（用于控制面板统计）
async function loadProjectsData() {
    try {
        const response = await fetch(`${API_BASE}/projects`);
        projects = await response.json();
    } catch (error) {
        console.error('Error loading projects:', error);
        showNotification('加载项目数据失败', 'error');
    }
}

// 工具函数
function getStatusClass(status) {
    switch(status) {
        case 'passed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        case 'pending': return 'bg-gray-100 text-gray-800';
        case 'running': return 'bg-blue-100 text-blue-800';
        case 'skipped': return 'bg-yellow-100 text-yellow-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'passed': return '已通过';
        case 'failed': return '失败';
        case 'pending': return '待执行';
        case 'running': return '执行中';
        case 'skipped': return '跳过';
        case 'draft': return '草稿';
        default: return '未知';
    }
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'high': return 'bg-red-100 text-red-800';
        case 'medium': return 'bg-yellow-100 text-yellow-800';
        case 'low': return 'bg-green-100 text-green-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getPriorityText(priority) {
    switch(priority) {
        case 'high': return '高';
        case 'medium': return '中';
        case 'low': return '低';
        default: return '未知';
    }
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 通知功能
function showNotification(message, type = 'info', duration = 3000) {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    
    notification.className = `mb-2 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${getNotificationClass(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${getNotificationIcon(type)} mr-2"></i>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 hover:opacity-75">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

function getNotificationClass(type) {
    switch(type) {
        case 'success': return 'bg-green-500 text-white';
        case 'error': return 'bg-red-500 text-white';
        case 'warning': return 'bg-yellow-500 text-white';
        default: return 'bg-blue-500 text-white';
    }
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

// 测试函数
function testProjectsFunction() {
    const output = document.getElementById('projects-test-output');
    output.innerHTML = '<p>测试项目管理功能...</p>';

    console.log('测试项目管理功能');
    console.log('loadProjectsPage函数存在:', typeof loadProjectsPage);

    if (typeof loadProjectsPage === 'function') {
        output.innerHTML += '<p class="text-green-600">loadProjectsPage函数存在</p>';
        try {
            loadProjectsPage();
            output.innerHTML += '<p class="text-green-600">loadProjectsPage函数调用成功</p>';
        } catch (error) {
            output.innerHTML += `<p class="text-red-600">loadProjectsPage函数调用失败: ${error.message}</p>`;
        }
    } else {
        output.innerHTML += '<p class="text-red-600">loadProjectsPage函数不存在</p>';
    }
}

// 全局函数，供其他模块使用
window.showNotification = showNotification;
window.getStatusClass = getStatusClass;
window.getStatusText = getStatusText;
window.getPriorityClass = getPriorityClass;
window.getPriorityText = getPriorityText;
window.formatDate = formatDate;
window.API_BASE = API_BASE;
window.testProjectsFunction = testProjectsFunction;
