// 全局变量
let currentPage = 'dashboard';
let projects = [];
let testcases = [];

// API 基础URL
const API_BASE = '/api';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 初始化导航
    initNavigation();
    
    // 初始化移动端菜单
    initMobileMenu();
    
    // 加载初始数据
    loadDashboardData();
    
    // 默认显示控制面板
    showPage('dashboard');
}

// 导航功能
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const pageId = this.getAttribute('data-page');
            showPage(pageId);
        });
    });
}

function showPage(pageId) {
    // 隐藏所有页面
    const pageContents = document.querySelectorAll('.page-content');
    pageContents.forEach(page => {
        page.classList.add('hidden');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + '-page');
    if (targetPage) {
        targetPage.classList.remove('hidden');
    }

    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('text-white', 'bg-blue-900');
        link.classList.add('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
    });

    // 激活当前导航
    const activeLink = document.querySelector(`[data-page="${pageId}"]`);
    if (activeLink) {
        activeLink.classList.remove('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
        activeLink.classList.add('text-white', 'bg-blue-900');
    }

    currentPage = pageId;

    // 根据页面加载相应数据
    switch(pageId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'testcases':
            loadTestcasesPage();
            break;
        case 'projects':
            loadProjectsPageInline();
            break;
    }
}

// 移动端菜单
function initMobileMenu() {
    const mobileMenuBtn = document.querySelector('button.md\\:hidden');
    const sidebar = document.querySelector('.sidebar');
    
    if(mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('hidden');
        });
    }
}

// 加载控制面板数据
async function loadDashboardData() {
    try {
        // 加载项目数据
        const projectsResponse = await fetch(`${API_BASE}/projects`);
        projects = await projectsResponse.json();
        
        // 加载测试用例数据
        const testcasesResponse = await fetch(`${API_BASE}/testcases`);
        testcases = await testcasesResponse.json();
        
        // 更新统计数据
        updateDashboardStats();
        
        // 显示最近测试用例
        displayRecentTestcases();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showNotification('加载数据失败', 'error');
    }
}

function updateDashboardStats() {
    // 更新项目总数
    document.getElementById('total-projects').textContent = projects.length;
    
    // 更新测试用例总数
    document.getElementById('total-testcases').textContent = testcases.length;
    
    // 计算成功率
    const passedTests = testcases.filter(tc => tc.status === 'passed').length;
    const totalTests = testcases.length;
    const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
    document.getElementById('success-rate').textContent = successRate + '%';
}

function displayRecentTestcases() {
    const container = document.getElementById('recent-testcases');
    
    if (testcases.length === 0) {
        container.innerHTML = `
            <div class="p-6 text-center text-gray-500">
                <i class="fas fa-inbox text-4xl mb-2"></i>
                <p>暂无测试用例</p>
            </div>
        `;
        return;
    }

    // 显示最近的5个测试用例
    const recentTestcases = testcases.slice(0, 5);
    
    container.innerHTML = recentTestcases.map(testcase => `
        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="viewTestcaseDetail('${testcase.id}')">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">${testcase.name}</h4>
                    <p class="text-sm text-gray-500 mt-1">${testcase.description || '无描述'}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="text-xs text-gray-500">项目: ${testcase.project_name || '未知'}</span>
                        <span class="text-xs text-gray-500">创建人: ${testcase.creator || '未知'}</span>
                    </div>
                </div>
                <div class="ml-4 flex flex-col items-end">
                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(testcase.status)}">
                        ${getStatusText(testcase.status)}
                    </span>
                    <span class="text-xs text-gray-500 mt-1">${formatDate(testcase.created_at)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// 加载项目数据（用于控制面板统计）
async function loadProjectsData() {
    try {
        const response = await fetch(`${API_BASE}/projects`);
        projects = await response.json();
    } catch (error) {
        console.error('Error loading projects:', error);
        showNotification('加载项目数据失败', 'error');
    }
}

// 工具函数
function getStatusClass(status) {
    switch(status) {
        case 'passed': return 'bg-green-100 text-green-800';
        case 'failed': return 'bg-red-100 text-red-800';
        case 'pending': return 'bg-gray-100 text-gray-800';
        case 'running': return 'bg-blue-100 text-blue-800';
        case 'skipped': return 'bg-yellow-100 text-yellow-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'passed': return '已通过';
        case 'failed': return '失败';
        case 'pending': return '待执行';
        case 'running': return '执行中';
        case 'skipped': return '跳过';
        case 'draft': return '草稿';
        default: return '未知';
    }
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'high': return 'bg-red-100 text-red-800';
        case 'medium': return 'bg-yellow-100 text-yellow-800';
        case 'low': return 'bg-green-100 text-green-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getPriorityText(priority) {
    switch(priority) {
        case 'high': return '高';
        case 'medium': return '中';
        case 'low': return '低';
        default: return '未知';
    }
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 通知功能
function showNotification(message, type = 'info', duration = 3000) {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    
    notification.className = `mb-2 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${getNotificationClass(type)}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${getNotificationIcon(type)} mr-2"></i>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 hover:opacity-75">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

function getNotificationClass(type) {
    switch(type) {
        case 'success': return 'bg-green-500 text-white';
        case 'error': return 'bg-red-500 text-white';
        case 'warning': return 'bg-yellow-500 text-white';
        default: return 'bg-blue-500 text-white';
    }
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

// 测试函数
function testProjectsFunction() {
    const output = document.getElementById('projects-test-output');
    output.innerHTML = '<p>测试项目管理功能...</p>';

    console.log('测试项目管理功能');
    console.log('projectsModuleLoaded:', window.projectsModuleLoaded);
    console.log('loadProjectsPage函数存在:', typeof window.loadProjectsPage);

    output.innerHTML += `<p>模块加载状态: ${window.projectsModuleLoaded ? '✅ 已加载' : '❌ 未加载'}</p>`;
    output.innerHTML += `<p>loadProjectsPage函数: ${typeof window.loadProjectsPage === 'function' ? '✅ 存在' : '❌ 不存在'}</p>`;

    if (typeof window.loadProjectsPage === 'function') {
        try {
            window.loadProjectsPage();
            output.innerHTML += '<p class="text-green-600">✅ loadProjectsPage函数调用成功</p>';
        } catch (error) {
            output.innerHTML += `<p class="text-red-600">❌ loadProjectsPage函数调用失败: ${error.message}</p>`;
        }
    } else {
        output.innerHTML += '<p class="text-red-600">❌ loadProjectsPage函数不存在</p>';
    }
}

// 内联项目管理功能
async function loadProjectsPageInline() {
    console.log('加载项目管理页面（内联版本）');

    try {
        // 加载项目数据并更新统计
        const response = await fetch(`${API_BASE}/projects`);
        const projects = await response.json();

        // 更新统计数据
        document.getElementById('total-projects-count').textContent = projects.length;
        document.getElementById('active-projects-count').textContent =
            projects.filter(p => p.status === 'active').length;
        document.getElementById('total-members-count').textContent =
            projects.reduce((sum, p) => sum + (p.member_count || 0), 0);
        document.getElementById('total-testcases-count').textContent =
            projects.reduce((sum, p) => sum + (p.testcase_count || 0), 0);

        // 显示项目列表
        loadProjectsList();

    } catch (error) {
        console.error('加载项目管理页面失败:', error);
        showNotification('加载项目管理页面失败', 'error');
    }
}

async function loadProjectsList() {
    const container = document.getElementById('projects-list-container');

    try {
        container.innerHTML = '<div class="text-center"><div class="loading mx-auto mb-2"></div><p>加载中...</p></div>';

        const response = await fetch(`${API_BASE}/projects`);
        const projects = await response.json();

        if (projects.length === 0) {
            container.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-folder-open text-4xl mb-4"></i>
                    <p class="mb-4">暂无项目</p>
                    <button onclick="createNewProject()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        创建第一个项目
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                ${projects.map(project => `
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-semibold text-gray-900">${project.name}</h4>
                            <span class="px-2 py-1 text-xs rounded-full ${getProjectStatusClass(project.status)}">
                                ${getProjectStatusText(project.status)}
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">${project.description || '无描述'}</p>
                        <div class="flex justify-between items-center text-sm text-gray-500">
                            <span><i class="fas fa-user mr-1"></i> ${project.owner || '未指定'}</span>
                            <span><i class="fas fa-users mr-1"></i> ${project.member_count || 0}</span>
                            <span><i class="fas fa-tasks mr-1"></i> ${project.testcase_count || 0}</span>
                        </div>
                        <div class="mt-3 flex justify-end space-x-2">
                            <button onclick="viewProject('${project.id}')" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button onclick="editProject('${project.id}')" class="text-green-600 hover:text-green-800 text-sm">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button onclick="deleteProject('${project.id}')" class="text-red-600 hover:text-red-800 text-sm">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

    } catch (error) {
        console.error('加载项目列表失败:', error);
        container.innerHTML = `
            <div class="text-center text-red-600 py-8">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p class="mb-4">加载项目列表失败</p>
                <button onclick="loadProjectsList()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    重试
                </button>
            </div>
        `;
    }
}

function getProjectStatusClass(status) {
    switch(status) {
        case 'active': return 'bg-green-100 text-green-800';
        case 'completed': return 'bg-blue-100 text-blue-800';
        case 'paused': return 'bg-yellow-100 text-yellow-800';
        case 'cancelled': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getProjectStatusText(status) {
    switch(status) {
        case 'active': return '进行中';
        case 'completed': return '已完成';
        case 'paused': return '已暂停';
        case 'cancelled': return '已取消';
        default: return '未知';
    }
}

function createNewProject() {
    showProjectModal('新建项目', {});
}

async function viewProject(id) {
    try {
        const response = await fetch(`${API_BASE}/projects/${id}`);
        if (!response.ok) throw new Error('Failed to load project');

        const project = await response.json();
        showProjectDetailModal(project);

    } catch (error) {
        console.error('Error loading project detail:', error);
        showNotification('加载项目详情失败', 'error');
    }
}

async function editProject(id) {
    try {
        const response = await fetch(`${API_BASE}/projects/${id}`);
        if (!response.ok) throw new Error('Failed to load project');

        const project = await response.json();
        showProjectModal('编辑项目', project);

    } catch (error) {
        console.error('Error loading project for edit:', error);
        showNotification('加载项目失败', 'error');
    }
}

// 项目表单模态框
function showProjectModal(title, project = {}) {
    const modalHtml = `
        <div id="project-form-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <form id="project-form" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 *</label>
                                <input id="project-name" type="text" required value="${project.name || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入项目名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人</label>
                                <input id="project-owner" type="text" value="${project.owner || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入负责人姓名">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
                            <textarea id="project-description" rows="3"
                                      class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入项目的详细描述">${project.description || ''}</textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                <select id="project-priority" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="high" ${project.priority === 'high' ? 'selected' : ''}>高</option>
                                    <option value="medium" ${project.priority === 'medium' || !project.priority ? 'selected' : ''}>中</option>
                                    <option value="low" ${project.priority === 'low' ? 'selected' : ''}>低</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                                <select id="project-status" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="active" ${project.status === 'active' || !project.status ? 'selected' : ''}>进行中</option>
                                    <option value="paused" ${project.status === 'paused' ? 'selected' : ''}>已暂停</option>
                                    <option value="completed" ${project.status === 'completed' ? 'selected' : ''}>已完成</option>
                                    <option value="cancelled" ${project.status === 'cancelled' ? 'selected' : ''}>已取消</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目标签</label>
                                <input id="project-tags" type="text" value="${project.tags ? project.tags.join(', ') : ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="用逗号分隔多个标签">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                                <input id="project-start-date" type="date" value="${project.start_date || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                                <input id="project-end-date" type="date" value="${project.end_date || ''}"
                                       class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4 border-t">
                            <button type="button" onclick="closeProjectModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <i class="fas fa-save mr-1"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    const form = document.getElementById('project-form');
    form.addEventListener('submit', (e) => handleProjectFormSubmit(e, project.id || null));
}

function closeProjectModal() {
    const modal = document.getElementById('project-form-modal');
    if (modal) {
        modal.remove();
    }
}

async function handleProjectFormSubmit(e, projectId = null) {
    e.preventDefault();

    console.log('表单提交处理开始', { projectId });

    // 收集表单数据
    const formData = {
        name: document.getElementById('project-name').value.trim(),
        description: document.getElementById('project-description').value.trim(),
        owner: document.getElementById('project-owner').value.trim(),
        priority: document.getElementById('project-priority').value,
        status: document.getElementById('project-status').value,
        start_date: document.getElementById('project-start-date').value,
        end_date: document.getElementById('project-end-date').value,
        tags: document.getElementById('project-tags').value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0)
    };

    // 验证必填字段
    if (!formData.name) {
        showNotification('请输入项目名称', 'error');
        return;
    }

    // 验证日期
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
        showNotification('开始日期不能晚于结束日期', 'error');
        return;
    }

    try {
        console.log('准备发送API请求', { formData, projectId });

        let response;
        if (projectId) {
            // 更新项目
            console.log('发送PUT请求更新项目');
            response = await fetch(`${API_BASE}/projects/${projectId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // 创建新项目
            console.log('发送POST请求创建项目');
            response = await fetch(`${API_BASE}/projects`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        console.log('API响应状态:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API请求失败:', response.status, errorText);
            throw new Error(`Failed to save project: ${response.status} ${errorText}`);
        }

        console.log('项目保存成功');
        showNotification(
            projectId ? '项目更新成功' : '项目创建成功',
            'success'
        );

        closeProjectModal();

        // 重新加载项目列表
        console.log('重新加载项目列表');
        loadProjectsList();
        loadProjectsPageInline(); // 更新统计数据

    } catch (error) {
        console.error('Error saving project:', error);
        showNotification('保存项目失败: ' + error.message, 'error');
    }
}

// 项目详情模态框
function showProjectDetailModal(project) {
    const modalHtml = `
        <div id="project-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-medium text-gray-900">项目详情</h3>
                        <button onclick="closeProjectDetailModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- 项目基本信息 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">${project.name}</h4>
                                <p class="text-gray-600">${project.description || '无描述'}</p>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">负责人:</span>
                                    <span class="text-sm font-medium">${project.owner || '未指定'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">状态:</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getProjectStatusClass(project.status)}">
                                        ${getProjectStatusText(project.status)}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">优先级:</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${getPriorityClass(project.priority)}">
                                        ${getPriorityText(project.priority)}
                                    </span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">开始日期:</span>
                                    <span class="text-sm font-medium">${project.start_date || '未设置'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">结束日期:</span>
                                    <span class="text-sm font-medium">${project.end_date || '未设置'}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">创建时间:</span>
                                    <span class="text-sm font-medium">${formatDate(project.created_at)}</span>
                                </div>
                            </div>
                        </div>

                        ${project.tags && project.tags.length > 0 ? `
                            <div class="mt-4">
                                <span class="text-sm text-gray-500 mr-2">标签:</span>
                                ${project.tags.map(tag => `
                                    <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded mr-1">${tag}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>

                    <!-- 统计信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">${project.member_count || 0}</div>
                            <div class="text-sm text-gray-500">团队成员</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">${project.testcase_count || 0}</div>
                            <div class="text-sm text-gray-500">测试用例</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600">${calculatePassRate(project.testcase_stats)}%</div>
                            <div class="text-sm text-gray-500">通过率</div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <button onclick="closeProjectDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            关闭
                        </button>
                        <button onclick="editProject('${project.id}')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i class="fas fa-edit mr-1"></i> 编辑项目
                        </button>
                        <button onclick="deleteProject('${project.id}')" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                            <i class="fas fa-trash mr-1"></i> 删除项目
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

function closeProjectDetailModal() {
    const modal = document.getElementById('project-detail-modal');
    if (modal) {
        modal.remove();
    }
}

function calculatePassRate(testcaseStats) {
    if (!testcaseStats || testcaseStats.length === 0) return 0;

    const total = testcaseStats.reduce((sum, stat) => sum + stat.count, 0);
    const passed = testcaseStats.find(stat => stat.status === 'passed');

    if (total === 0) return 0;
    return Math.round(((passed ? passed.count : 0) / total) * 100);
}

// 删除项目功能
async function deleteProject(id) {
    if (!confirm('确定要删除这个项目吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/projects/${id}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to delete project');
        }

        showNotification('项目删除成功', 'success');

        // 关闭详情模态框（如果打开的话）
        closeProjectDetailModal();

        // 重新加载项目列表
        loadProjectsList();
        loadProjectsPageInline(); // 更新统计数据

    } catch (error) {
        console.error('Error deleting project:', error);
        if (error.message.includes('existing test cases')) {
            showNotification('无法删除包含测试用例的项目，请先删除相关测试用例', 'error');
        } else {
            showNotification('删除项目失败: ' + error.message, 'error');
        }
    }
}

// 全局函数，供其他模块使用
window.showNotification = showNotification;
window.getStatusClass = getStatusClass;
window.getStatusText = getStatusText;
window.getPriorityClass = getPriorityClass;
window.getPriorityText = getPriorityText;
window.formatDate = formatDate;
window.API_BASE = API_BASE;
window.testProjectsFunction = testProjectsFunction;
window.loadProjectsPageInline = loadProjectsPageInline;
window.loadProjectsList = loadProjectsList;
window.createNewProject = createNewProject;
window.viewProject = viewProject;
window.editProject = editProject;
window.deleteProject = deleteProject;
window.showProjectModal = showProjectModal;
window.closeProjectModal = closeProjectModal;
window.showProjectDetailModal = showProjectDetailModal;
window.closeProjectDetailModal = closeProjectDetailModal;
