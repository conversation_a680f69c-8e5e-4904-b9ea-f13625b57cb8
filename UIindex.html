<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .progress-bar {
            transition: width 1s ease-in-out;
        }
        .page-content {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .nav-link {
            transition: all 0.2s ease-in-out;
        }
        .settings-nav-link {
            transition: all 0.2s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- 侧边栏 -->
        <div class="sidebar bg-blue-800 text-white w-64 flex-shrink-0 md:block hidden">
            <div class="p-4 flex items-center space-x-2">
                <i class="fas fa-robot text-2xl"></i>
                <h1 class="text-xl font-bold">自动化测试平台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-4 py-2 text-blue-200 uppercase text-xs font-semibold">主菜单</div>
                <a href="#" class="nav-link block px-4 py-3 text-white bg-blue-900" data-page="dashboard">
                    <i class="fas fa-tachometer-alt mr-2"></i> 控制面板
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="projects">
                    <i class="fas fa-project-diagram mr-2"></i> 项目管理
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="testcases">
                    <i class="fas fa-tasks mr-2"></i> 测试用例
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="execution">
                    <i class="fas fa-play-circle mr-2"></i> 测试执行
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="reports">
                    <i class="fas fa-chart-bar mr-2"></i> 测试报告
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="team">
                    <i class="fas fa-users mr-2"></i> 团队管理
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="settings">
                    <i class="fas fa-cog mr-2"></i> 系统设置
                </a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-6 py-3">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-600 mr-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div class="relative">
                            <input type="text" placeholder="搜索项目、测试用例..." 
                                   class="pl-10 pr-4 py-2 border rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -mt-2 -mr-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                        <div class="flex items-center">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="ml-2 text-gray-700">张测试</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <main class="p-6">
                <!-- 控制面板页面 -->
                <div id="dashboard-page" class="page-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">控制面板</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建测试
                        </button>
                    </div>

                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">项目总数</p>
                                <h3 class="text-3xl font-bold mt-2">24</h3>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月增加 12%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">测试用例</p>
                                <h3 class="text-3xl font-bold mt-2">1,284</h3>
                            </div>
                            <div class="bg-green-100 p-3 rounded-full">
                                <i class="fas fa-tasks text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月增加 8%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">今日执行</p>
                                <h3 class="text-3xl font-bold mt-2">56</h3>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-full">
                                <i class="fas fa-play-circle text-purple-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-red-600">
                            <i class="fas fa-arrow-down mr-1"></i> 比昨日减少 5%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">成功率</p>
                                <h3 class="text-3xl font-bold mt-2">92%</h3>
                            </div>
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月提升 3%
                        </div>
                    </div>
                </div>

                <!-- 图表和最近测试 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- 测试通过率图表 -->
                    <div class="lg:col-span-2 bg-white rounded-lg shadow p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">测试通过率趋势</h3>
                            <select class="border rounded px-3 py-1 text-sm">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                            </select>
                        </div>
                        <div class="h-64">
                            <!-- 这里应该是图表，用CSS模拟 -->
                            <div class="relative h-full w-full border-b border-l border-gray-200">
                                <div class="absolute bottom-0 left-0 right-0 flex justify-between items-end h-5/6 px-4">
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 60%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 70%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 80%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 85%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 90%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 92%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 95%;"></div>
                                </div>
                                <div class="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500 px-4">
                                    <span>周一</span>
                                    <span>周二</span>
                                    <span>周三</span>
                                    <span>周四</span>
                                    <span>周五</span>
                                    <span>周六</span>
                                    <span>周日</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近测试执行 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">最近测试执行</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">用户登录测试套件</p>
                                    <p class="text-sm text-gray-500">3分钟前 · 通过率 100%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-red-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-times text-red-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">订单流程测试</p>
                                    <p class="text-sm text-gray-500">25分钟前 · 通过率 87%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">API接口测试</p>
                                    <p class="text-sm text-gray-500">1小时前 · 通过率 100%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-yellow-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-exclamation text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">性能压力测试</p>
                                    <p class="text-sm text-gray-500">2小时前 · 通过率 95%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">UI自动化测试</p>
                                    <p class="text-sm text-gray-500">3小时前 · 通过率 98%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">项目列表</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            查看全部 <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                    <div class="divide-y">
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">电商平台测试项目</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-15 14:30</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">328</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 94%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">94%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">移动端APP测试</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-14 09:15</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">156</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 89%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">89%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">后台管理系统测试</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-13 16:45</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">243</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 96%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">96%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目管理页面 -->
                <div id="projects-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">项目管理</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建项目
                        </button>
                    </div>

                    <!-- 项目筛选和搜索 -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部状态</option>
                                    <option>进行中</option>
                                    <option>已完成</option>
                                    <option>已暂停</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部类型</option>
                                    <option>Web测试</option>
                                    <option>移动端测试</option>
                                    <option>API测试</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">负责人</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部成员</option>
                                    <option>张测试</option>
                                    <option>李开发</option>
                                    <option>王产品</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索项目</label>
                                <input type="text" placeholder="输入项目名称..." class="w-full border rounded-lg px-3 py-2">
                            </div>
                        </div>
                    </div>

                    <!-- 项目列表 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                            <div class="p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900">电商平台测试项目</h3>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">进行中</span>
                                </div>
                                <p class="text-gray-600 text-sm mb-4">全面测试电商平台的用户注册、登录、购物车、订单等核心功能</p>
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-xs text-gray-500">测试用例</p>
                                        <p class="font-semibold">328</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">通过率</p>
                                        <p class="font-semibold text-green-600">94%</p>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex -space-x-2">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/men/46.jpg" alt="">
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看详情</button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                            <div class="p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900">移动端APP测试</h3>
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">待开始</span>
                                </div>
                                <p class="text-gray-600 text-sm mb-4">iOS和Android移动应用的功能测试、兼容性测试和性能测试</p>
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-xs text-gray-500">测试用例</p>
                                        <p class="font-semibold">156</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">通过率</p>
                                        <p class="font-semibold text-green-600">89%</p>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex -space-x-2">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/women/32.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/men/54.jpg" alt="">
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看详情</button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                            <div class="p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h3 class="text-lg font-semibold text-gray-900">后台管理系统测试</h3>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">进行中</span>
                                </div>
                                <p class="text-gray-600 text-sm mb-4">管理后台的权限管理、数据统计、系统配置等功能测试</p>
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-xs text-gray-500">测试用例</p>
                                        <p class="font-semibold">243</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">通过率</p>
                                        <p class="font-semibold text-green-600">96%</p>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex -space-x-2">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/women/68.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/men/76.jpg" alt="">
                                        <img class="w-6 h-6 rounded-full border-2 border-white" src="https://randomuser.me/api/portraits/women/82.jpg" alt="">
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试用例页面 -->
                <div id="testcases-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">测试用例管理</h2>
                        <div class="flex space-x-3">
                            <button id="batch-execute-btn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-play mr-2"></i> 批量执行
                            </button>
                            <button id="batch-delete-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-trash mr-2"></i> 批量删除
                            </button>
                            <button id="new-testcase-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                                <i class="fas fa-plus mr-2"></i> 新建用例
                            </button>
                        </div>
                    </div>

                    <!-- 筛选和搜索 -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属项目</label>
                                <select id="filter-project" class="w-full border rounded-lg px-3 py-2">
                                    <option value="">全部项目</option>
                                    <option value="电商平台测试">电商平台测试</option>
                                    <option value="移动端APP测试">移动端APP测试</option>
                                    <option value="后台管理系统">后台管理系统</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用例状态</label>
                                <select id="filter-status" class="w-full border rounded-lg px-3 py-2">
                                    <option value="">全部状态</option>
                                    <option value="已通过">已通过</option>
                                    <option value="失败">失败</option>
                                    <option value="待执行">待执行</option>
                                    <option value="跳过">跳过</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                <select id="filter-priority" class="w-full border rounded-lg px-3 py-2">
                                    <option value="">全部优先级</option>
                                    <option value="高">高</option>
                                    <option value="中">中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">创建人</label>
                                <select id="filter-creator" class="w-full border rounded-lg px-3 py-2">
                                    <option value="">全部成员</option>
                                    <option value="张测试">张测试</option>
                                    <option value="李开发">李开发</option>
                                    <option value="王产品">王产品</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索用例</label>
                                <input id="search-testcase" type="text" placeholder="输入用例名称..." class="w-full border rounded-lg px-3 py-2">
                            </div>
                        </div>
                    </div>

                    <!-- 测试用例列表 -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-6 py-4 border-b bg-gray-50">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800">测试用例列表</h3>
                                <div class="flex space-x-2">
                                    <button class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded">
                                        <i class="fas fa-download mr-1"></i> 导出
                                    </button>
                                    <button class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded">
                                        <i class="fas fa-upload mr-1"></i> 导入
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input id="select-all-checkbox" type="checkbox" class="rounded">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用例名称</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属项目</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后执行</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="testcase-table-body" class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50 testcase-row" data-testcase-id="1">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded testcase-checkbox">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600" onclick="viewTestcaseDetail(1)">用户登录功能测试</div>
                                            <div class="text-sm text-gray-500">验证用户名密码登录流程</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">电商平台测试</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已通过</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张测试</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15 14:30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button onclick="viewTestcaseDetail(1)" class="text-blue-600 hover:text-blue-900" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="editTestcase(1)" class="text-green-600 hover:text-green-900" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="executeTestcase(1)" class="text-purple-600 hover:text-purple-900" title="执行">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button onclick="deleteTestcase(1)" class="text-red-600 hover:text-red-900" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 testcase-row" data-testcase-id="2">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded testcase-checkbox">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600" onclick="viewTestcaseDetail(2)">购物车添加商品测试</div>
                                            <div class="text-sm text-gray-500">验证商品添加到购物车功能</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">电商平台测试</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">中</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">失败</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李开发</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-14 16:45</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button onclick="viewTestcaseDetail(2)" class="text-blue-600 hover:text-blue-900" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="editTestcase(2)" class="text-green-600 hover:text-green-900" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="executeTestcase(2)" class="text-purple-600 hover:text-purple-900" title="执行">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button onclick="deleteTestcase(2)" class="text-red-600 hover:text-red-900" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 testcase-row" data-testcase-id="3">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded testcase-checkbox">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600" onclick="viewTestcaseDetail(3)">订单支付流程测试</div>
                                            <div class="text-sm text-gray-500">验证订单支付完整流程</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">电商平台测试</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">待执行</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王产品</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button onclick="viewTestcaseDetail(3)" class="text-blue-600 hover:text-blue-900" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="editTestcase(3)" class="text-green-600 hover:text-green-900" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="executeTestcase(3)" class="text-purple-600 hover:text-purple-900" title="执行">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button onclick="deleteTestcase(3)" class="text-red-600 hover:text-red-900" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-3 bg-gray-50 border-t">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-700">
                                    显示 1-10 条，共 328 条记录
                                </div>
                                <div class="flex space-x-1">
                                    <button class="px-3 py-1 border rounded text-sm hover:bg-gray-100">上一页</button>
                                    <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
                                    <button class="px-3 py-1 border rounded text-sm hover:bg-gray-100">2</button>
                                    <button class="px-3 py-1 border rounded text-sm hover:bg-gray-100">3</button>
                                    <button class="px-3 py-1 border rounded text-sm hover:bg-gray-100">下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试执行页面 -->
                <div id="execution-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">测试执行</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-play mr-2"></i> 开始执行
                        </button>
                    </div>

                    <!-- 执行配置 -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">执行配置</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择项目</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>电商平台测试项目</option>
                                    <option>移动端APP测试</option>
                                    <option>后台管理系统测试</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行环境</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>测试环境</option>
                                    <option>预发布环境</option>
                                    <option>生产环境</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行模式</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>并行执行</option>
                                    <option>串行执行</option>
                                    <option>分组执行</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded mr-2">
                                <span class="text-sm text-gray-700">失败时立即停止</span>
                            </label>
                        </div>
                    </div>

                    <!-- 执行状态 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 当前执行状态 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">当前执行状态</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">总进度</span>
                                    <span class="text-sm font-medium">65/100 (65%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                </div>
                                <div class="grid grid-cols-2 gap-4 mt-4">
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-green-600">52</p>
                                        <p class="text-sm text-gray-500">已通过</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-2xl font-bold text-red-600">8</p>
                                        <p class="text-sm text-gray-500">失败</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 执行统计 -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">执行统计</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">开始时间</span>
                                    <span class="text-sm font-medium">2023-06-15 14:30:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">预计结束</span>
                                    <span class="text-sm font-medium">2023-06-15 15:45:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">已用时间</span>
                                    <span class="text-sm font-medium">45分钟</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">平均耗时</span>
                                    <span class="text-sm font-medium">42秒/用例</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 执行日志 -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800">执行日志</h3>
                                <div class="flex space-x-2">
                                    <button class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded text-sm">
                                        <i class="fas fa-pause mr-1"></i> 暂停
                                    </button>
                                    <button class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded text-sm">
                                        <i class="fas fa-stop mr-1"></i> 停止
                                    </button>
                                    <button class="text-gray-600 hover:text-gray-800 px-3 py-1 border rounded text-sm">
                                        <i class="fas fa-download mr-1"></i> 导出日志
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                                <div>[2023-06-15 14:30:00] 开始执行测试套件: 电商平台测试项目</div>
                                <div>[2023-06-15 14:30:05] ✓ 用户登录功能测试 - 通过 (2.3s)</div>
                                <div>[2023-06-15 14:30:08] ✓ 用户注册功能测试 - 通过 (3.1s)</div>
                                <div>[2023-06-15 14:30:12] ✗ 购物车添加商品测试 - 失败 (4.2s)</div>
                                <div class="text-red-400">&nbsp;&nbsp;&nbsp;&nbsp;错误: 元素未找到 - #add-to-cart-btn</div>
                                <div>[2023-06-15 14:30:16] ✓ 商品搜索功能测试 - 通过 (1.8s)</div>
                                <div>[2023-06-15 14:30:19] ✓ 商品详情页测试 - 通过 (2.5s)</div>
                                <div>[2023-06-15 14:30:23] ✗ 订单创建测试 - 失败 (3.7s)</div>
                                <div class="text-red-400">&nbsp;&nbsp;&nbsp;&nbsp;错误: 网络超时</div>
                                <div>[2023-06-15 14:30:27] ✓ 用户个人中心测试 - 通过 (2.1s)</div>
                                <div>[2023-06-15 14:30:30] 正在执行: 订单支付流程测试...</div>
                                <div class="animate-pulse">█</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试报告页面 -->
                <div id="reports-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">测试报告</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-file-export mr-2"></i> 生成报告
                        </button>
                    </div>

                    <!-- 报告筛选 -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部项目</option>
                                    <option>电商平台测试</option>
                                    <option>移动端APP测试</option>
                                    <option>后台管理系统</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>最近7天</option>
                                    <option>最近30天</option>
                                    <option>最近90天</option>
                                    <option>自定义</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">报告类型</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部类型</option>
                                    <option>功能测试</option>
                                    <option>性能测试</option>
                                    <option>接口测试</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行人</label>
                                <select class="w-full border rounded-lg px-3 py-2">
                                    <option>全部成员</option>
                                    <option>张测试</option>
                                    <option>李开发</option>
                                    <option>王产品</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 报告概览 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">总报告数</p>
                                    <h3 class="text-2xl font-bold">156</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">平均通过率</p>
                                    <h3 class="text-2xl font-bold text-green-600">92.5%</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">平均执行时间</p>
                                    <h3 class="text-2xl font-bold">45分钟</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-red-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-bug text-red-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">发现缺陷</p>
                                    <h3 class="text-2xl font-bold text-red-600">23</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报告列表 -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-800">测试报告列表</h3>
                        </div>
                        <div class="divide-y">
                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 mb-2">电商平台测试项目 - 2023年6月第2周报告</h4>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-500">执行时间:</span>
                                                <span class="font-medium">2023-06-15 14:30</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">用例总数:</span>
                                                <span class="font-medium">328</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">通过率:</span>
                                                <span class="font-medium text-green-600">94%</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">执行人:</span>
                                                <span class="font-medium">张测试</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-500 h-2 rounded-full" style="width: 94%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-6 flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-eye mr-1"></i> 查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-download mr-1"></i> 下载
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 mb-2">移动端APP测试 - 功能回归测试报告</h4>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-500">执行时间:</span>
                                                <span class="font-medium">2023-06-14 09:15</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">用例总数:</span>
                                                <span class="font-medium">156</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">通过率:</span>
                                                <span class="font-medium text-yellow-600">89%</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">执行人:</span>
                                                <span class="font-medium">李开发</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 89%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-6 flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-eye mr-1"></i> 查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-download mr-1"></i> 下载
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 mb-2">后台管理系统测试 - 权限管理模块测试</h4>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                            <div>
                                                <span class="text-gray-500">执行时间:</span>
                                                <span class="font-medium">2023-06-13 16:45</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">用例总数:</span>
                                                <span class="font-medium">243</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">通过率:</span>
                                                <span class="font-medium text-green-600">96%</span>
                                            </div>
                                            <div>
                                                <span class="text-gray-500">执行人:</span>
                                                <span class="font-medium">王产品</span>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-500 h-2 rounded-full" style="width: 96%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ml-6 flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-eye mr-1"></i> 查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 px-3 py-1 border rounded text-sm">
                                            <i class="fas fa-download mr-1"></i> 下载
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 团队管理页面 -->
                <div id="team-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">团队管理</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-user-plus mr-2"></i> 邀请成员
                        </button>
                    </div>

                    <!-- 团队统计 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-users text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">团队成员</p>
                                    <h3 class="text-2xl font-bold">12</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-green-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-user-check text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">活跃成员</p>
                                    <h3 class="text-2xl font-bold text-green-600">9</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-yellow-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-crown text-yellow-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">管理员</p>
                                    <h3 class="text-2xl font-bold">3</h3>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="bg-purple-100 p-3 rounded-full mr-4">
                                    <i class="fas fa-user-graduate text-purple-600 text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">新成员</p>
                                    <h3 class="text-2xl font-bold">2</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 成员列表 -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-6 py-4 border-b">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800">团队成员</h3>
                                <div class="flex space-x-2">
                                    <input type="text" placeholder="搜索成员..." class="border rounded-lg px-3 py-1 text-sm">
                                    <select class="border rounded-lg px-3 py-1 text-sm">
                                        <option>全部角色</option>
                                        <option>管理员</option>
                                        <option>测试工程师</option>
                                        <option>开发工程师</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="divide-y">
                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="h-12 w-12 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                        <div class="ml-4">
                                            <h4 class="text-lg font-medium text-gray-900">张测试</h4>
                                            <p class="text-sm text-gray-500"><EMAIL></p>
                                            <div class="flex items-center mt-1">
                                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full mr-2">管理员</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">在线</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">加入时间</p>
                                        <p class="font-medium">2023-01-15</p>
                                        <p class="text-sm text-gray-500 mt-1">最后活跃: 2分钟前</p>
                                    </div>
                                    <div class="ml-6">
                                        <button class="text-blue-600 hover:text-blue-800 mr-3">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">移除</button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="h-12 w-12 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                        <div class="ml-4">
                                            <h4 class="text-lg font-medium text-gray-900">李开发</h4>
                                            <p class="text-sm text-gray-500"><EMAIL></p>
                                            <div class="flex items-center mt-1">
                                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mr-2">开发工程师</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">在线</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">加入时间</p>
                                        <p class="font-medium">2023-02-20</p>
                                        <p class="text-sm text-gray-500 mt-1">最后活跃: 15分钟前</p>
                                    </div>
                                    <div class="ml-6">
                                        <button class="text-blue-600 hover:text-blue-800 mr-3">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">移除</button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="h-12 w-12 rounded-full" src="https://randomuser.me/api/portraits/men/46.jpg" alt="">
                                        <div class="ml-4">
                                            <h4 class="text-lg font-medium text-gray-900">王产品</h4>
                                            <p class="text-sm text-gray-500"><EMAIL></p>
                                            <div class="flex items-center mt-1">
                                                <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mr-2">产品经理</span>
                                                <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">离线</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">加入时间</p>
                                        <p class="font-medium">2023-03-10</p>
                                        <p class="text-sm text-gray-500 mt-1">最后活跃: 2小时前</p>
                                    </div>
                                    <div class="ml-6">
                                        <button class="text-blue-600 hover:text-blue-800 mr-3">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">移除</button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-6 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="h-12 w-12 rounded-full" src="https://randomuser.me/api/portraits/women/68.jpg" alt="">
                                        <div class="ml-4">
                                            <h4 class="text-lg font-medium text-gray-900">赵测试</h4>
                                            <p class="text-sm text-gray-500"><EMAIL></p>
                                            <div class="flex items-center mt-1">
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full mr-2">测试工程师</span>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">在线</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">加入时间</p>
                                        <p class="font-medium">2023-04-05</p>
                                        <p class="text-sm text-gray-500 mt-1">最后活跃: 5分钟前</p>
                                    </div>
                                    <div class="ml-6">
                                        <button class="text-blue-600 hover:text-blue-800 mr-3">编辑</button>
                                        <button class="text-red-600 hover:text-red-800">移除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置页面 -->
                <div id="settings-page" class="page-content hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">系统设置</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-save mr-2"></i> 保存设置
                        </button>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 设置菜单 -->
                        <div class="bg-white rounded-lg shadow">
                            <div class="p-6 border-b">
                                <h3 class="text-lg font-semibold text-gray-800">设置分类</h3>
                            </div>
                            <nav class="p-4">
                                <a href="#" class="settings-nav-link block px-4 py-2 text-blue-600 bg-blue-50 rounded mb-2" data-setting="general">
                                    <i class="fas fa-cog mr-2"></i> 基本设置
                                </a>
                                <a href="#" class="settings-nav-link block px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded mb-2" data-setting="notification">
                                    <i class="fas fa-bell mr-2"></i> 通知设置
                                </a>
                                <a href="#" class="settings-nav-link block px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded mb-2" data-setting="security">
                                    <i class="fas fa-shield-alt mr-2"></i> 安全设置
                                </a>
                                <a href="#" class="settings-nav-link block px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded mb-2" data-setting="integration">
                                    <i class="fas fa-plug mr-2"></i> 集成设置
                                </a>
                                <a href="#" class="settings-nav-link block px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded mb-2" data-setting="backup">
                                    <i class="fas fa-database mr-2"></i> 备份设置
                                </a>
                            </nav>
                        </div>

                        <!-- 设置内容 -->
                        <div class="lg:col-span-2">
                            <!-- 基本设置 -->
                            <div id="general-settings" class="settings-content bg-white rounded-lg shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-6">基本设置</h3>
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">系统名称</label>
                                        <input type="text" value="自动化测试平台" class="w-full border rounded-lg px-3 py-2">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">系统描述</label>
                                        <textarea rows="3" class="w-full border rounded-lg px-3 py-2">企业级自动化测试管理平台，提供完整的测试生命周期管理</textarea>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">默认语言</label>
                                        <select class="w-full border rounded-lg px-3 py-2">
                                            <option>中文简体</option>
                                            <option>English</option>
                                            <option>日本語</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">时区设置</label>
                                        <select class="w-full border rounded-lg px-3 py-2">
                                            <option>Asia/Shanghai (UTC+8)</option>
                                            <option>America/New_York (UTC-5)</option>
                                            <option>Europe/London (UTC+0)</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded mr-2" checked>
                                            <span class="text-sm text-gray-700">启用系统维护模式</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 通知设置 -->
                            <div id="notification-settings" class="settings-content bg-white rounded-lg shadow p-6 hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-6">通知设置</h3>
                                <div class="space-y-6">
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">邮件通知</h4>
                                        <div class="space-y-2">
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded mr-2" checked>
                                                <span class="text-sm text-gray-700">测试执行完成通知</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded mr-2" checked>
                                                <span class="text-sm text-gray-700">测试失败通知</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded mr-2">
                                                <span class="text-sm text-gray-700">每日测试报告</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">系统通知</h4>
                                        <div class="space-y-2">
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded mr-2" checked>
                                                <span class="text-sm text-gray-700">新成员加入通知</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="rounded mr-2">
                                                <span class="text-sm text-gray-700">系统更新通知</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">SMTP服务器</label>
                                        <input type="text" placeholder="smtp.company.com" class="w-full border rounded-lg px-3 py-2">
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">端口</label>
                                            <input type="text" value="587" class="w-full border rounded-lg px-3 py-2">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">发件人邮箱</label>
                                            <input type="email" placeholder="<EMAIL>" class="w-full border rounded-lg px-3 py-2">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 安全设置 -->
                            <div id="security-settings" class="settings-content bg-white rounded-lg shadow p-6 hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-6">安全设置</h3>
                                <div class="space-y-6">
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">密码策略</h4>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">最小密码长度</label>
                                                <input type="number" value="8" class="w-full border rounded-lg px-3 py-2">
                                            </div>
                                            <div class="space-y-2">
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="rounded mr-2" checked>
                                                    <span class="text-sm text-gray-700">必须包含大写字母</span>
                                                </label>
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="rounded mr-2" checked>
                                                    <span class="text-sm text-gray-700">必须包含数字</span>
                                                </label>
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="rounded mr-2">
                                                    <span class="text-sm text-gray-700">必须包含特殊字符</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">会话管理</h4>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">会话超时时间(分钟)</label>
                                                <input type="number" value="30" class="w-full border rounded-lg px-3 py-2">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">最大并发会话</label>
                                                <input type="number" value="3" class="w-full border rounded-lg px-3 py-2">
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" class="rounded mr-2" checked>
                                            <span class="text-sm text-gray-700">启用双因素认证</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- 新建/编辑测试用例模态框 -->
            <div id="testcase-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="modal-title" class="text-lg font-medium text-gray-900">新建测试用例</h3>
                            <button onclick="closeTestcaseModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <form id="testcase-form" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用例名称 *</label>
                                    <input id="testcase-name" type="text" required class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">所属项目 *</label>
                                    <select id="testcase-project" required class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择项目</option>
                                        <option value="电商平台测试">电商平台测试</option>
                                        <option value="移动端APP测试">移动端APP测试</option>
                                        <option value="后台管理系统">后台管理系统</option>
                                    </select>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                                    <select id="testcase-priority" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="高">高</option>
                                        <option value="中" selected>中</option>
                                        <option value="低">低</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用例类型</label>
                                    <select id="testcase-type" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="功能测试">功能测试</option>
                                        <option value="接口测试">接口测试</option>
                                        <option value="性能测试">性能测试</option>
                                        <option value="UI测试">UI测试</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">执行方式</label>
                                    <select id="testcase-execution" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="自动">自动</option>
                                        <option value="手动">手动</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用例描述</label>
                                <textarea id="testcase-description" rows="3" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入测试用例的详细描述"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">前置条件</label>
                                <textarea id="testcase-precondition" rows="2" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="执行此用例前需要满足的条件"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">测试步骤</label>
                                <div id="test-steps-container">
                                    <div class="test-step flex items-center space-x-2 mb-2">
                                        <span class="text-sm text-gray-500 w-8">1.</span>
                                        <input type="text" class="flex-1 border rounded px-3 py-2 text-sm" placeholder="输入测试步骤">
                                        <button type="button" onclick="removeTestStep(this)" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-minus-circle"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" onclick="addTestStep()" class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-plus-circle mr-1"></i> 添加步骤
                                </button>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">预期结果</label>
                                <textarea id="testcase-expected" rows="3" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="描述执行测试步骤后的预期结果"></textarea>
                            </div>
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closeTestcaseModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                    取消
                                </button>
                                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    保存
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 查看测试用例详情模态框 -->
            <div id="testcase-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">测试用例详情</h3>
                            <button onclick="closeTestcaseDetailModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div id="testcase-detail-content" class="space-y-4">
                            <!-- 详情内容将通过JavaScript动态填充 -->
                        </div>
                        <div class="flex justify-end space-x-3 pt-4">
                            <button onclick="closeTestcaseDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                关闭
                            </button>
                            <button onclick="editTestcaseFromDetail()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                编辑
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 删除确认模态框 -->
            <div id="delete-confirm-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                <div class="relative top-1/3 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
                        <p class="text-sm text-gray-500 mb-4">
                            您确定要删除选中的测试用例吗？此操作不可撤销。
                        </p>
                        <div class="flex justify-center space-x-3">
                            <button onclick="closeDeleteConfirmModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 执行测试用例模态框 -->
            <div id="execute-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                <div class="relative top-1/4 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">执行测试用例</h3>
                            <button onclick="closeExecuteModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行环境</label>
                                <select id="execute-environment" class="w-full border rounded-lg px-3 py-2">
                                    <option value="test">测试环境</option>
                                    <option value="staging">预发布环境</option>
                                    <option value="production">生产环境</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行备注</label>
                                <textarea id="execute-notes" rows="3" class="w-full border rounded-lg px-3 py-2" placeholder="可选：添加执行备注"></textarea>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 pt-4">
                            <button onclick="closeExecuteModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button onclick="startExecution()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                开始执行
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <script>
        // 页面切换和交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 页面切换功能
            const navLinks = document.querySelectorAll('.nav-link');
            const pageContents = document.querySelectorAll('.page-content');

            function showPage(pageId) {
                // 隐藏所有页面
                pageContents.forEach(page => {
                    page.classList.add('hidden');
                });

                // 显示目标页面
                const targetPage = document.getElementById(pageId + '-page');
                if (targetPage) {
                    targetPage.classList.remove('hidden');
                }

                // 更新导航状态
                navLinks.forEach(link => {
                    link.classList.remove('text-white', 'bg-blue-900');
                    link.classList.add('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
                });

                // 激活当前导航
                const activeLink = document.querySelector(`[data-page="${pageId}"]`);
                if (activeLink) {
                    activeLink.classList.remove('text-blue-200', 'hover:text-white', 'hover:bg-blue-700');
                    activeLink.classList.add('text-white', 'bg-blue-900');
                }
            }

            // 绑定导航点击事件
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageId = this.getAttribute('data-page');
                    showPage(pageId);
                });
            });

            // 设置页面内的子导航切换
            const settingsNavLinks = document.querySelectorAll('.settings-nav-link');
            const settingsContents = document.querySelectorAll('.settings-content');

            settingsNavLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const settingId = this.getAttribute('data-setting');

                    // 隐藏所有设置内容
                    settingsContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // 显示目标设置内容
                    const targetContent = document.getElementById(settingId + '-settings');
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }

                    // 更新设置导航状态
                    settingsNavLinks.forEach(navLink => {
                        navLink.classList.remove('text-blue-600', 'bg-blue-50');
                        navLink.classList.add('text-gray-600');
                    });

                    // 激活当前设置导航
                    this.classList.remove('text-gray-600');
                    this.classList.add('text-blue-600', 'bg-blue-50');
                });
            });

            // 模拟图表数据加载动画
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });

            // 移动端菜单切换
            const mobileMenuBtn = document.querySelector('button.md\\:hidden');
            const sidebar = document.querySelector('.sidebar');

            if(mobileMenuBtn && sidebar) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('hidden');
                });
            }

            // 默认显示控制面板
            showPage('dashboard');

            // 测试用例相关功能初始化
            initTestcaseFunctions();
        });

        // 测试用例功能
        let currentTestcaseId = null;
        let selectedTestcases = [];

        function initTestcaseFunctions() {
            // 全选功能
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const checkboxes = document.querySelectorAll('.testcase-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedTestcases();
                });
            }

            // 单个复选框变化
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('testcase-checkbox')) {
                    updateSelectedTestcases();
                }
            });

            // 筛选功能
            const filterElements = ['filter-project', 'filter-status', 'filter-priority', 'filter-creator'];
            filterElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', filterTestcases);
                }
            });

            // 搜索功能
            const searchInput = document.getElementById('search-testcase');
            if (searchInput) {
                searchInput.addEventListener('input', filterTestcases);
            }

            // 新建用例按钮
            const newTestcaseBtn = document.getElementById('new-testcase-btn');
            if (newTestcaseBtn) {
                newTestcaseBtn.addEventListener('click', openNewTestcaseModal);
            }

            // 批量操作按钮
            const batchExecuteBtn = document.getElementById('batch-execute-btn');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');
            if (batchExecuteBtn) {
                batchExecuteBtn.addEventListener('click', batchExecuteTestcases);
            }
            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', batchDeleteTestcases);
            }

            // 表单提交
            const testcaseForm = document.getElementById('testcase-form');
            if (testcaseForm) {
                testcaseForm.addEventListener('submit', saveTestcase);
            }
        }

        function updateSelectedTestcases() {
            const checkboxes = document.querySelectorAll('.testcase-checkbox:checked');
            selectedTestcases = Array.from(checkboxes).map(cb =>
                cb.closest('.testcase-row').dataset.testcaseId
            );

            const batchExecuteBtn = document.getElementById('batch-execute-btn');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');

            if (batchExecuteBtn && batchDeleteBtn) {
                const hasSelected = selectedTestcases.length > 0;
                batchExecuteBtn.disabled = !hasSelected;
                batchDeleteBtn.disabled = !hasSelected;
            }

            // 更新全选状态
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const allCheckboxes = document.querySelectorAll('.testcase-checkbox');
            if (selectAllCheckbox && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = selectedTestcases.length === allCheckboxes.length;
                selectAllCheckbox.indeterminate = selectedTestcases.length > 0 && selectedTestcases.length < allCheckboxes.length;
            }
        }

        function filterTestcases() {
            const project = document.getElementById('filter-project').value;
            const status = document.getElementById('filter-status').value;
            const priority = document.getElementById('filter-priority').value;
            const creator = document.getElementById('filter-creator').value;
            const search = document.getElementById('search-testcase').value.toLowerCase();

            const rows = document.querySelectorAll('.testcase-row');
            rows.forEach(row => {
                const projectCell = row.cells[2].textContent;
                const statusCell = row.cells[4].textContent;
                const priorityCell = row.cells[3].textContent;
                const creatorCell = row.cells[5].textContent;
                const nameCell = row.cells[1].textContent.toLowerCase();

                const matchProject = !project || projectCell.includes(project);
                const matchStatus = !status || statusCell.includes(status);
                const matchPriority = !priority || priorityCell.includes(priority);
                const matchCreator = !creator || creatorCell.includes(creator);
                const matchSearch = !search || nameCell.includes(search);

                if (matchProject && matchStatus && matchPriority && matchCreator && matchSearch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 模态框操作函数
        function openNewTestcaseModal() {
            currentTestcaseId = null;
            document.getElementById('modal-title').textContent = '新建测试用例';
            document.getElementById('testcase-form').reset();
            clearTestSteps();
            addTestStep();
            document.getElementById('testcase-modal').classList.remove('hidden');
        }

        function editTestcase(id) {
            currentTestcaseId = id;
            document.getElementById('modal-title').textContent = '编辑测试用例';

            // 模拟加载测试用例数据
            const testcaseData = getTestcaseData(id);
            fillTestcaseForm(testcaseData);

            document.getElementById('testcase-modal').classList.remove('hidden');
        }

        function closeTestcaseModal() {
            document.getElementById('testcase-modal').classList.add('hidden');
            currentTestcaseId = null;
        }

        function viewTestcaseDetail(id) {
            const testcaseData = getTestcaseData(id);
            fillTestcaseDetail(testcaseData);
            document.getElementById('testcase-detail-modal').classList.remove('hidden');
        }

        function closeTestcaseDetailModal() {
            document.getElementById('testcase-detail-modal').classList.add('hidden');
        }

        function editTestcaseFromDetail() {
            closeTestcaseDetailModal();
            editTestcase(currentTestcaseId);
        }

        function deleteTestcase(id) {
            currentTestcaseId = id;
            document.getElementById('delete-confirm-modal').classList.remove('hidden');
        }

        function closeDeleteConfirmModal() {
            document.getElementById('delete-confirm-modal').classList.add('hidden');
            currentTestcaseId = null;
        }

        function confirmDelete() {
            if (currentTestcaseId) {
                // 模拟删除操作
                const row = document.querySelector(`[data-testcase-id="${currentTestcaseId}"]`);
                if (row) {
                    row.remove();
                    showNotification('测试用例删除成功', 'success');
                }
            }
            closeDeleteConfirmModal();
        }

        function executeTestcase(id) {
            currentTestcaseId = id;
            document.getElementById('execute-modal').classList.remove('hidden');
        }

        function closeExecuteModal() {
            document.getElementById('execute-modal').classList.add('hidden');
            currentTestcaseId = null;
        }

        function startExecution() {
            const environment = document.getElementById('execute-environment').value;
            const notes = document.getElementById('execute-notes').value;

            // 模拟执行操作
            showNotification(`测试用例开始在${environment}环境执行`, 'info');
            closeExecuteModal();
        }

        // 批量操作
        function batchExecuteTestcases() {
            if (selectedTestcases.length === 0) return;

            showNotification(`开始批量执行 ${selectedTestcases.length} 个测试用例`, 'info');
        }

        function batchDeleteTestcases() {
            if (selectedTestcases.length === 0) return;

            currentTestcaseId = null; // 批量删除时不设置单个ID
            document.getElementById('delete-confirm-modal').classList.remove('hidden');
        }

        // 测试步骤管理
        function addTestStep() {
            const container = document.getElementById('test-steps-container');
            const stepCount = container.children.length + 1;

            const stepDiv = document.createElement('div');
            stepDiv.className = 'test-step flex items-center space-x-2 mb-2';
            stepDiv.innerHTML = `
                <span class="text-sm text-gray-500 w-8">${stepCount}.</span>
                <input type="text" class="flex-1 border rounded px-3 py-2 text-sm" placeholder="输入测试步骤">
                <button type="button" onclick="removeTestStep(this)" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-minus-circle"></i>
                </button>
            `;

            container.appendChild(stepDiv);
        }

        function removeTestStep(button) {
            const container = document.getElementById('test-steps-container');
            if (container.children.length > 1) {
                button.closest('.test-step').remove();
                updateStepNumbers();
            }
        }

        function updateStepNumbers() {
            const steps = document.querySelectorAll('.test-step');
            steps.forEach((step, index) => {
                step.querySelector('span').textContent = `${index + 1}.`;
            });
        }

        function clearTestSteps() {
            const container = document.getElementById('test-steps-container');
            container.innerHTML = '';
        }

        // 表单操作
        function saveTestcase(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const testcaseData = {
                name: document.getElementById('testcase-name').value,
                project: document.getElementById('testcase-project').value,
                priority: document.getElementById('testcase-priority').value,
                type: document.getElementById('testcase-type').value,
                execution: document.getElementById('testcase-execution').value,
                description: document.getElementById('testcase-description').value,
                precondition: document.getElementById('testcase-precondition').value,
                expected: document.getElementById('testcase-expected').value,
                steps: getTestSteps()
            };

            // 模拟保存操作
            if (currentTestcaseId) {
                showNotification('测试用例更新成功', 'success');
            } else {
                showNotification('测试用例创建成功', 'success');
            }

            closeTestcaseModal();
        }

        function getTestSteps() {
            const stepInputs = document.querySelectorAll('.test-step input');
            return Array.from(stepInputs).map(input => input.value).filter(step => step.trim());
        }

        // 模拟数据获取
        function getTestcaseData(id) {
            const testcases = {
                1: {
                    name: '用户登录功能测试',
                    project: '电商平台测试',
                    priority: '高',
                    type: '功能测试',
                    execution: '自动',
                    status: '已通过',
                    creator: '张测试',
                    description: '验证用户名密码登录流程',
                    precondition: '用户已注册且账号状态正常',
                    steps: ['打开登录页面', '输入用户名和密码', '点击登录按钮'],
                    expected: '用户成功登录，跳转到首页',
                    lastExecution: '2023-06-15 14:30'
                },
                2: {
                    name: '购物车添加商品测试',
                    project: '电商平台测试',
                    priority: '中',
                    type: '功能测试',
                    execution: '自动',
                    status: '失败',
                    creator: '李开发',
                    description: '验证商品添加到购物车功能',
                    precondition: '用户已登录，商品库存充足',
                    steps: ['浏览商品页面', '选择商品规格', '点击加入购物车'],
                    expected: '商品成功添加到购物车，购物车数量更新',
                    lastExecution: '2023-06-14 16:45'
                },
                3: {
                    name: '订单支付流程测试',
                    project: '电商平台测试',
                    priority: '高',
                    type: '功能测试',
                    execution: '手动',
                    status: '待执行',
                    creator: '王产品',
                    description: '验证订单支付完整流程',
                    precondition: '用户已登录，购物车有商品',
                    steps: ['进入购物车', '点击结算', '填写收货信息', '选择支付方式', '完成支付'],
                    expected: '订单支付成功，生成订单号',
                    lastExecution: '-'
                }
            };
            return testcases[id] || {};
        }

        function fillTestcaseForm(data) {
            document.getElementById('testcase-name').value = data.name || '';
            document.getElementById('testcase-project').value = data.project || '';
            document.getElementById('testcase-priority').value = data.priority || '中';
            document.getElementById('testcase-type').value = data.type || '功能测试';
            document.getElementById('testcase-execution').value = data.execution || '自动';
            document.getElementById('testcase-description').value = data.description || '';
            document.getElementById('testcase-precondition').value = data.precondition || '';
            document.getElementById('testcase-expected').value = data.expected || '';

            // 填充测试步骤
            clearTestSteps();
            if (data.steps && data.steps.length > 0) {
                data.steps.forEach((step, index) => {
                    if (index === 0) {
                        addTestStep();
                    } else {
                        addTestStep();
                    }
                    const stepInputs = document.querySelectorAll('.test-step input');
                    stepInputs[index].value = step;
                });
            } else {
                addTestStep();
            }
        }

        function fillTestcaseDetail(data) {
            const content = document.getElementById('testcase-detail-content');
            content.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">用例名称</label>
                        <p class="mt-1 text-sm text-gray-900">${data.name}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">所属项目</label>
                        <p class="mt-1 text-sm text-gray-900">${data.project}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">优先级</label>
                        <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityClass(data.priority)}">${data.priority}</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">状态</label>
                        <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(data.status)}">${data.status}</span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">用例类型</label>
                        <p class="mt-1 text-sm text-gray-900">${data.type}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">执行方式</label>
                        <p class="mt-1 text-sm text-gray-900">${data.execution}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">创建人</label>
                        <p class="mt-1 text-sm text-gray-900">${data.creator}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">最后执行</label>
                        <p class="mt-1 text-sm text-gray-900">${data.lastExecution}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">用例描述</label>
                    <p class="mt-1 text-sm text-gray-900">${data.description}</p>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">前置条件</label>
                    <p class="mt-1 text-sm text-gray-900">${data.precondition}</p>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">测试步骤</label>
                    <ol class="mt-1 text-sm text-gray-900 list-decimal list-inside space-y-1">
                        ${data.steps ? data.steps.map(step => `<li>${step}</li>`).join('') : '<li>无测试步骤</li>'}
                    </ol>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">预期结果</label>
                    <p class="mt-1 text-sm text-gray-900">${data.expected}</p>
                </div>
            `;
            currentTestcaseId = Object.keys(getTestcaseData()).find(key => getTestcaseData()[key].name === data.name);
        }

        function getPriorityClass(priority) {
            switch(priority) {
                case '高': return 'bg-red-100 text-red-800';
                case '中': return 'bg-yellow-100 text-yellow-800';
                case '低': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusClass(status) {
            switch(status) {
                case '已通过': return 'bg-green-100 text-green-800';
                case '失败': return 'bg-red-100 text-red-800';
                case '待执行': return 'bg-gray-100 text-gray-800';
                case '跳过': return 'bg-yellow-100 text-yellow-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        // 通知功能
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${getNotificationClass(type)}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${getNotificationIcon(type)} mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        function getNotificationClass(type) {
            switch(type) {
                case 'success': return 'bg-green-500 text-white';
                case 'error': return 'bg-red-500 text-white';
                case 'warning': return 'bg-yellow-500 text-white';
                default: return 'bg-blue-500 text-white';
            }
        }

        function getNotificationIcon(type) {
            switch(type) {
                case 'success': return 'fa-check-circle';
                case 'error': return 'fa-exclamation-circle';
                case 'warning': return 'fa-exclamation-triangle';
                default: return 'fa-info-circle';
            }
        }
    </script>
</body>
</html>