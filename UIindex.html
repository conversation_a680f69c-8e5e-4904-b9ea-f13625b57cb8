<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .progress-bar {
            transition: width 1s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- 侧边栏 -->
        <div class="sidebar bg-blue-800 text-white w-64 flex-shrink-0 md:block hidden">
            <div class="p-4 flex items-center space-x-2">
                <i class="fas fa-robot text-2xl"></i>
                <h1 class="text-xl font-bold">自动化测试平台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-4 py-2 text-blue-200 uppercase text-xs font-semibold">主菜单</div>
                <a href="#" class="block px-4 py-3 text-white bg-blue-900">
                    <i class="fas fa-tachometer-alt mr-2"></i> 控制面板
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-project-diagram mr-2"></i> 项目管理
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-tasks mr-2"></i> 测试用例
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-play-circle mr-2"></i> 测试执行
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-chart-bar mr-2"></i> 测试报告
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-users mr-2"></i> 团队管理
                </a>
                <a href="#" class="block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700">
                    <i class="fas fa-cog mr-2"></i> 系统设置
                </a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-6 py-3">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-600 mr-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div class="relative">
                            <input type="text" placeholder="搜索项目、测试用例..." 
                                   class="pl-10 pr-4 py-2 border rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -mt-2 -mr-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                        <div class="flex items-center">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="ml-2 text-gray-700">张测试</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <main class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">控制面板</h2>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                        <i class="fas fa-plus mr-2"></i> 新建测试
                    </button>
                </div>

                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">项目总数</p>
                                <h3 class="text-3xl font-bold mt-2">24</h3>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月增加 12%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">测试用例</p>
                                <h3 class="text-3xl font-bold mt-2">1,284</h3>
                            </div>
                            <div class="bg-green-100 p-3 rounded-full">
                                <i class="fas fa-tasks text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月增加 8%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">今日执行</p>
                                <h3 class="text-3xl font-bold mt-2">56</h3>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-full">
                                <i class="fas fa-play-circle text-purple-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-red-600">
                            <i class="fas fa-arrow-down mr-1"></i> 比昨日减少 5%
                        </div>
                    </div>

                    <div class="dashboard-card bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500">成功率</p>
                                <h3 class="text-3xl font-bold mt-2">92%</h3>
                            </div>
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4 text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i> 比上月提升 3%
                        </div>
                    </div>
                </div>

                <!-- 图表和最近测试 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- 测试通过率图表 -->
                    <div class="lg:col-span-2 bg-white rounded-lg shadow p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">测试通过率趋势</h3>
                            <select class="border rounded px-3 py-1 text-sm">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                            </select>
                        </div>
                        <div class="h-64">
                            <!-- 这里应该是图表，用CSS模拟 -->
                            <div class="relative h-full w-full border-b border-l border-gray-200">
                                <div class="absolute bottom-0 left-0 right-0 flex justify-between items-end h-5/6 px-4">
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 60%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 70%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 80%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 85%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 90%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 92%;"></div>
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 95%;"></div>
                                </div>
                                <div class="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500 px-4">
                                    <span>周一</span>
                                    <span>周二</span>
                                    <span>周三</span>
                                    <span>周四</span>
                                    <span>周五</span>
                                    <span>周六</span>
                                    <span>周日</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近测试执行 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">最近测试执行</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">用户登录测试套件</p>
                                    <p class="text-sm text-gray-500">3分钟前 · 通过率 100%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-red-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-times text-red-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">订单流程测试</p>
                                    <p class="text-sm text-gray-500">25分钟前 · 通过率 87%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">API接口测试</p>
                                    <p class="text-sm text-gray-500">1小时前 · 通过率 100%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-yellow-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-exclamation text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">性能压力测试</p>
                                    <p class="text-sm text-gray-500">2小时前 · 通过率 95%</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-full mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium">UI自动化测试</p>
                                    <p class="text-sm text-gray-500">3小时前 · 通过率 98%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">项目列表</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            查看全部 <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                    <div class="divide-y">
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">电商平台测试项目</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-15 14:30</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">328</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 94%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">94%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">移动端APP测试</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-14 09:15</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">156</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 89%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">89%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6 hover:bg-gray-50 flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-900">后台管理系统测试</h4>
                                <p class="text-sm text-gray-500 mt-1">最后执行: 2023-06-13 16:45</p>
                            </div>
                            <div class="flex items-center">
                                <div class="mr-6">
                                    <p class="text-sm text-gray-500">测试用例</p>
                                    <p class="font-medium">243</p>
                                </div>
                                <div class="w-32">
                                    <p class="text-sm text-gray-500 mb-1">通过率</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 96%"></div>
                                    </div>
                                    <p class="text-xs text-right mt-1">96%</p>
                                </div>
                                <button class="ml-6 text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟图表数据加载动画
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
            });

            // 移动端菜单切换
            const mobileMenuBtn = document.querySelector('button.md\\:hidden');
            const sidebar = document.querySelector('.sidebar');
            
            if(mobileMenuBtn && sidebar) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('hidden');
                });
            }
        });
    </script>
</body>
</html>