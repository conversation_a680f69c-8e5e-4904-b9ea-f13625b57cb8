<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .progress-bar {
            transition: width 1s ease-in-out;
        }
        .page-content {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .nav-link {
            transition: all 0.2s ease-in-out;
        }
        .settings-nav-link {
            transition: all 0.2s ease-in-out;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .modal-overlay {
            backdrop-filter: blur(2px);
        }
        .test-step input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="flex h-screen overflow-hidden">
        <!-- 侧边栏 -->
        <div class="sidebar bg-blue-800 text-white w-64 flex-shrink-0 md:block hidden">
            <div class="p-4 flex items-center space-x-2">
                <i class="fas fa-robot text-2xl"></i>
                <h1 class="text-xl font-bold">自动化测试平台</h1>
            </div>
            <nav class="mt-6">
                <div class="px-4 py-2 text-blue-200 uppercase text-xs font-semibold">主菜单</div>
                <a href="#" class="nav-link block px-4 py-3 text-white bg-blue-900" data-page="dashboard">
                    <i class="fas fa-tachometer-alt mr-2"></i> 控制面板
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="projects">
                    <i class="fas fa-project-diagram mr-2"></i> 项目管理
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="testcases">
                    <i class="fas fa-tasks mr-2"></i> 测试用例
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="execution">
                    <i class="fas fa-play-circle mr-2"></i> 测试执行
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="reports">
                    <i class="fas fa-chart-bar mr-2"></i> 测试报告
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="team">
                    <i class="fas fa-users mr-2"></i> 团队管理
                </a>
                <a href="#" class="nav-link block px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-700" data-page="settings">
                    <i class="fas fa-cog mr-2"></i> 系统设置
                </a>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-6 py-3">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-600 mr-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div class="relative">
                            <input type="text" placeholder="搜索项目、测试用例..." 
                                   class="pl-10 pr-4 py-2 border rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="text-gray-600 hover:text-gray-800">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -mt-2 -mr-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                        <div class="flex items-center">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="h-8 w-8 rounded-full">
                            <span class="ml-2 text-gray-700">等等何在</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <main class="p-6">
                <!-- 控制面板页面 -->
                <div id="dashboard-page" class="page-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">控制面板</h2>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建测试
                        </button>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="dashboard-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">项目总数</p>
                                    <h3 id="total-projects" class="text-3xl font-bold mt-2">-</h3>
                                </div>
                                <div class="bg-blue-100 p-3 rounded-full">
                                    <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">测试用例</p>
                                    <h3 id="total-testcases" class="text-3xl font-bold mt-2">-</h3>
                                </div>
                                <div class="bg-green-100 p-3 rounded-full">
                                    <i class="fas fa-tasks text-green-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">今日执行</p>
                                    <h3 class="text-3xl font-bold mt-2">0</h3>
                                </div>
                                <div class="bg-purple-100 p-3 rounded-full">
                                    <i class="fas fa-play-circle text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card bg-white rounded-lg shadow p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500">成功率</p>
                                    <h3 id="success-rate" class="text-3xl font-bold mt-2">-</h3>
                                </div>
                                <div class="bg-yellow-100 p-3 rounded-full">
                                    <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近测试用例 -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-lg font-semibold text-gray-800">最近测试用例</h3>
                        </div>
                        <div id="recent-testcases" class="divide-y">
                            <div class="p-6 text-center text-gray-500">
                                <div class="loading mx-auto mb-2"></div>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试用例页面 -->
                <div id="testcases-page" class="page-content hidden">
                    <!-- 测试用例内容将通过JavaScript动态加载 -->
                </div>

                <!-- 其他页面占位符 -->
                <div id="projects-page" class="page-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">项目管理</h2>
                    <p class="text-gray-600">项目管理功能开发中...</p>
                </div>

                <div id="execution-page" class="page-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">测试执行</h2>
                    <p class="text-gray-600">测试执行功能开发中...</p>
                </div>

                <div id="reports-page" class="page-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">测试报告</h2>
                    <p class="text-gray-600">测试报告功能开发中...</p>
                </div>

                <div id="team-page" class="page-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">团队管理</h2>
                    <p class="text-gray-600">团队管理功能开发中...</p>
                </div>

                <div id="settings-page" class="page-content hidden">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">系统设置</h2>
                    <p class="text-gray-600">系统设置功能开发中...</p>
                </div>
            </main>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

    <script src="js/app.js"></script>
    <script src="js/testcases.js"></script>
</body>
</html>
